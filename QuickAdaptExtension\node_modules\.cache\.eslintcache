[{"E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "5", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "6", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "7", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "9", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "10", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "11", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "12", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "13", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "14", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "15", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "16", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "17", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "18", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "19", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "20", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "21", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "22", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "23", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "24", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "25", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "27", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "29", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "30", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "31", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "32", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "33", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "34", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "35", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "36", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "37", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "38", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "39", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "40", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "41", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "42", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "43", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "44", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "45", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "46", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "47", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "48", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "49", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "50", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "51", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "52", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "53", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "54", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "55", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "56", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "57", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "58", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "59", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "60", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "61", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "62", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "63", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "64", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "65", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "66", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "67", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "68", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "69", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "70", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "71", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "72", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "73", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "74", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "75", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "76", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "77", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "78", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "79", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "80", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "81", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "82", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "83", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "84", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "85", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "86", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "87", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "88", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "89", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "90", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "91", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "92", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "93", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts": "94"}, {"size": 604, "mtime": 1748929949904, "results": "95", "hashOfConfig": "96"}, {"size": 440, "mtime": 1748929949904, "results": "97", "hashOfConfig": "96"}, {"size": 2367, "mtime": 1752757647829, "results": "98", "hashOfConfig": "96"}, {"size": 3359, "mtime": 1752814225145, "results": "99", "hashOfConfig": "96"}, {"size": 243070, "mtime": 1752832604988, "results": "100", "hashOfConfig": "96"}, {"size": 6890, "mtime": 1752814224988, "results": "101", "hashOfConfig": "96"}, {"size": 2750, "mtime": 1752814225129, "results": "102", "hashOfConfig": "96"}, {"size": 3112, "mtime": 1752571609553, "results": "103", "hashOfConfig": "96"}, {"size": 392608, "mtime": 1752814225145, "results": "104", "hashOfConfig": "96"}, {"size": 3927, "mtime": 1748930023076, "results": "105", "hashOfConfig": "96"}, {"size": 6144, "mtime": 1749531263982, "results": "106", "hashOfConfig": "96"}, {"size": 40511, "mtime": 1752819793548, "results": "107", "hashOfConfig": "96"}, {"size": 5077, "mtime": 1752728294207, "results": "108", "hashOfConfig": "96"}, {"size": 3731, "mtime": 1752571609521, "results": "109", "hashOfConfig": "96"}, {"size": 13563, "mtime": 1752814225129, "results": "110", "hashOfConfig": "96"}, {"size": 13085, "mtime": 1752728294234, "results": "111", "hashOfConfig": "96"}, {"size": 28388, "mtime": 1752814225129, "results": "112", "hashOfConfig": "96"}, {"size": 1898, "mtime": 1748930023076, "results": "113", "hashOfConfig": "96"}, {"size": 1954, "mtime": 1751432283612, "results": "114", "hashOfConfig": "96"}, {"size": 9031, "mtime": 1752814225129, "results": "115", "hashOfConfig": "96"}, {"size": 296570, "mtime": 1752571609475, "results": "116", "hashOfConfig": "96"}, {"size": 193, "mtime": 1748929949654, "results": "117", "hashOfConfig": "96"}, {"size": 9064, "mtime": 1752571609490, "results": "118", "hashOfConfig": "96"}, {"size": 30700, "mtime": 1752814225004, "results": "119", "hashOfConfig": "96"}, {"size": 3012, "mtime": 1752728294177, "results": "120", "hashOfConfig": "96"}, {"size": 2606, "mtime": 1752814224973, "results": "121", "hashOfConfig": "96"}, {"size": 33372, "mtime": 1752728294198, "results": "122", "hashOfConfig": "96"}, {"size": 23270, "mtime": 1752814354035, "results": "123", "hashOfConfig": "96"}, {"size": 13556, "mtime": 1752571609475, "results": "124", "hashOfConfig": "96"}, {"size": 26724, "mtime": 1752814224988, "results": "125", "hashOfConfig": "96"}, {"size": 49705, "mtime": 1751532053846, "results": "126", "hashOfConfig": "96"}, {"size": 7599, "mtime": 1752571609568, "results": "127", "hashOfConfig": "96"}, {"size": 30050, "mtime": 1752757801221, "results": "128", "hashOfConfig": "96"}, {"size": 11525, "mtime": 1752832604988, "results": "129", "hashOfConfig": "96"}, {"size": 24200, "mtime": 1751432283612, "results": "130", "hashOfConfig": "96"}, {"size": 4880, "mtime": 1750229130169, "results": "131", "hashOfConfig": "96"}, {"size": 9238, "mtime": 1748930023061, "results": "132", "hashOfConfig": "96"}, {"size": 1297, "mtime": 1748930023061, "results": "133", "hashOfConfig": "96"}, {"size": 1248, "mtime": 1748929949920, "results": "134", "hashOfConfig": "96"}, {"size": 14238, "mtime": 1748930023076, "results": "135", "hashOfConfig": "96"}, {"size": 2997, "mtime": 1752571609537, "results": "136", "hashOfConfig": "96"}, {"size": 3285, "mtime": 1752728294211, "results": "137", "hashOfConfig": "96"}, {"size": 2750, "mtime": 1752757497860, "results": "138", "hashOfConfig": "96"}, {"size": 955, "mtime": 1752814224973, "results": "139", "hashOfConfig": "96"}, {"size": 19907, "mtime": 1752814411947, "results": "140", "hashOfConfig": "96"}, {"size": 743, "mtime": 1748929949654, "results": "141", "hashOfConfig": "96"}, {"size": 24904, "mtime": 1752728294221, "results": "142", "hashOfConfig": "96"}, {"size": 2608, "mtime": 1748930023061, "results": "143", "hashOfConfig": "96"}, {"size": 30047, "mtime": 1752837230464, "results": "144", "hashOfConfig": "96"}, {"size": 7772, "mtime": 1752571609553, "results": "145", "hashOfConfig": "96"}, {"size": 16105, "mtime": 1752757497891, "results": "146", "hashOfConfig": "96"}, {"size": 29292, "mtime": 1752814225004, "results": "147", "hashOfConfig": "96"}, {"size": 6245, "mtime": 1748929949857, "results": "148", "hashOfConfig": "96"}, {"size": 2034, "mtime": 1752814224988, "results": "149", "hashOfConfig": "96"}, {"size": 29616, "mtime": 1752571609475, "results": "150", "hashOfConfig": "96"}, {"size": 1962, "mtime": 1748929949654, "results": "151", "hashOfConfig": "96"}, {"size": 27206, "mtime": 1752728294171, "results": "152", "hashOfConfig": "96"}, {"size": 2401, "mtime": 1752571609506, "results": "153", "hashOfConfig": "96"}, {"size": 702, "mtime": 1752571609506, "results": "154", "hashOfConfig": "96"}, {"size": 13889, "mtime": 1752571609506, "results": "155", "hashOfConfig": "96"}, {"size": 19040, "mtime": 1752571609537, "results": "156", "hashOfConfig": "96"}, {"size": 6625, "mtime": 1752571609537, "results": "157", "hashOfConfig": "96"}, {"size": 20321, "mtime": 1752738005916, "results": "158", "hashOfConfig": "96"}, {"size": 3236, "mtime": 1748929949779, "results": "159", "hashOfConfig": "96"}, {"size": 2848, "mtime": 1748929949811, "results": "160", "hashOfConfig": "96"}, {"size": 15285, "mtime": 1752571609490, "results": "161", "hashOfConfig": "96"}, {"size": 15217, "mtime": 1752571609506, "results": "162", "hashOfConfig": "96"}, {"size": 11208, "mtime": 1752571609537, "results": "163", "hashOfConfig": "96"}, {"size": 16370, "mtime": 1752571609537, "results": "164", "hashOfConfig": "96"}, {"size": 8476, "mtime": 1752571609537, "results": "165", "hashOfConfig": "96"}, {"size": 15571, "mtime": 1752814225129, "results": "166", "hashOfConfig": "96"}, {"size": 16126, "mtime": 1749557445376, "results": "167", "hashOfConfig": "96"}, {"size": 32671, "mtime": 1752819793487, "results": "168", "hashOfConfig": "96"}, {"size": 60407, "mtime": 1752571609475, "results": "169", "hashOfConfig": "96"}, {"size": 26698, "mtime": 1752571609490, "results": "170", "hashOfConfig": "96"}, {"size": 5258, "mtime": 1752571609553, "results": "171", "hashOfConfig": "96"}, {"size": 883, "mtime": 1748929949889, "results": "172", "hashOfConfig": "96"}, {"size": 2196, "mtime": 1752571609521, "results": "173", "hashOfConfig": "96"}, {"size": 7943, "mtime": 1748930023061, "results": "174", "hashOfConfig": "96"}, {"size": 491, "mtime": 1751432283612, "results": "175", "hashOfConfig": "96"}, {"size": 5504, "mtime": 1752571609506, "results": "176", "hashOfConfig": "96"}, {"size": 33137, "mtime": 1752814225113, "results": "177", "hashOfConfig": "96"}, {"size": 37236, "mtime": 1752814225098, "results": "178", "hashOfConfig": "96"}, {"size": 2931, "mtime": 1749010760558, "results": "179", "hashOfConfig": "96"}, {"size": 2669, "mtime": 1748929949748, "results": "180", "hashOfConfig": "96"}, {"size": 17277, "mtime": 1752814224988, "results": "181", "hashOfConfig": "96"}, {"size": 27631, "mtime": 1752728294148, "results": "182", "hashOfConfig": "96"}, {"size": 10829, "mtime": 1752819793504, "results": "183", "hashOfConfig": "96"}, {"size": 14592, "mtime": 1752571609490, "results": "184", "hashOfConfig": "96"}, {"size": 7289, "mtime": 1752571609568, "results": "185", "hashOfConfig": "96"}, {"size": 5493, "mtime": 1752571609568, "results": "186", "hashOfConfig": "96"}, {"size": 876, "mtime": 1752571609568, "results": "187", "hashOfConfig": "96"}, {"size": 5767, "mtime": 1752728294174, "results": "188", "hashOfConfig": "96"}, {"size": 677, "mtime": 1752814225129, "results": "189", "hashOfConfig": "96"}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c51j82", {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 226, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 41, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 48, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 63, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 42, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["472", "473"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["474"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["475", "476", "477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["701", "702", "703", "704", "705", "706"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["707", "708"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["729"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["730"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["772"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["813", "814", "815", "816"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["817", "818", "819"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["820", "821", "822", "823", "824", "825", "826", "827"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["855", "856", "857", "858"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1119", "1120", "1121", "1122", "1123", "1124"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1125", "1126", "1127", "1128"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1129", "1130"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1131", "1132", "1133"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1134", "1135"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1136"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1137", "1138", "1139"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1152", "1153", "1154"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1155"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1194", "1195", "1196", "1197", "1198"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1324", "1325", "1326", "1327"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1328", "1329", "1330", "1331", "1332", "1333"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1354", "1355", "1356"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1357", "1358"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1359", "1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1636", "1637"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1802", "1803"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["1874"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["1875", "1876"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts", ["1877"], [], {"ruleId": "1878", "severity": 1, "message": "1879", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1882", "line": 9, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "1883", "line": 1, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "1884", "line": 1, "column": 58, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 65}, {"ruleId": "1878", "severity": 1, "message": "1885", "line": 5, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1886", "line": 6, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1887", "line": 7, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "1888", "line": 12, "column": 28, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 40}, {"ruleId": "1878", "severity": 1, "message": "1889", "line": 17, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 9}, {"ruleId": "1878", "severity": 1, "message": "1890", "line": 22, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 22, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "1891", "line": 23, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 23, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1892", "line": 24, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1893", "line": 25, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1894", "line": 26, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1895", "line": 27, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 27, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1896", "line": 28, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 28, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1897", "line": 29, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 29, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "1898", "line": 30, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 30, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "1899", "line": 31, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 31, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1900", "line": 32, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 32, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1901", "line": 33, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 33, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1902", "line": 34, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 34, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1903", "line": 35, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 35, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1904", "line": 37, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 37, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1905", "line": 38, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 38, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "1906", "line": 39, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 39, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "1907", "line": 40, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 40, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "1908", "line": 44, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 44, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "1909", "line": 50, "column": 20, "nodeType": "1880", "messageId": "1881", "endLine": 50, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "1910", "line": 60, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "1911", "line": 61, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 61, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "1912", "line": 68, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 68, "endColumn": 6}, {"ruleId": "1878", "severity": 1, "message": "1913", "line": 69, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 69, "endColumn": 6}, {"ruleId": "1878", "severity": 1, "message": "1914", "line": 76, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 76, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "1915", "line": 78, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 78, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1916", "line": 79, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 79, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "1917", "line": 79, "column": 30, "nodeType": "1880", "messageId": "1881", "endLine": 79, "endColumn": 39}, {"ruleId": "1878", "severity": 1, "message": "1918", "line": 82, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 82, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "1919", "line": 83, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 83, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1920", "line": 84, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 84, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1921", "line": 88, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 88, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "1922", "line": 90, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 90, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "1923", "line": 96, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 96, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1924", "line": 103, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 103, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "1925", "line": 106, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 106, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "1926", "line": 107, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 107, "endColumn": 37}, {"ruleId": "1878", "severity": 1, "message": "1927", "line": 112, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 112, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "1928", "line": 112, "column": 21, "nodeType": "1880", "messageId": "1881", "endLine": 112, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "1929", "line": 115, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 115, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1930", "line": 122, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 122, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1931", "line": 135, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 135, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1932", "line": 198, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 198, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1933", "line": 215, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 215, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1934", "line": 223, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 223, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "1935", "line": 379, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 379, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1936", "line": 414, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 414, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "1937", "line": 416, "column": 6, "nodeType": "1880", "messageId": "1881", "endLine": 416, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "1938", "line": 418, "column": 6, "nodeType": "1880", "messageId": "1881", "endLine": 418, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "1939", "line": 434, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 434, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1940", "line": 435, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 435, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "1941", "line": 437, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 437, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "1942", "line": 440, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 440, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "1943", "line": 444, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 444, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "1944", "line": 445, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 445, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "1945", "line": 456, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 456, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "1946", "line": 457, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 457, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1947", "line": 458, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 458, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1948", "line": 460, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 460, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "1949", "line": 460, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 460, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "1950", "line": 465, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 465, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "1951", "line": 465, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 465, "endColumn": 38}, {"ruleId": "1878", "severity": 1, "message": "1952", "line": 467, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 467, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1953", "line": 467, "column": 19, "nodeType": "1880", "messageId": "1881", "endLine": 467, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "1954", "line": 470, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 470, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1955", "line": 470, "column": 24, "nodeType": "1880", "messageId": "1881", "endLine": 470, "endColumn": 40}, {"ruleId": "1878", "severity": 1, "message": "1956", "line": 471, "column": 19, "nodeType": "1880", "messageId": "1881", "endLine": 471, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "1957", "line": 476, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 476, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "1958", "line": 476, "column": 29, "nodeType": "1880", "messageId": "1881", "endLine": 476, "endColumn": 50}, {"ruleId": "1878", "severity": 1, "message": "1959", "line": 483, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 483, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1960", "line": 483, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 483, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "1961", "line": 485, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 485, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "1962", "line": 485, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 485, "endColumn": 41}, {"ruleId": "1878", "severity": 1, "message": "1963", "line": 487, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 487, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1964", "line": 487, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 487, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "1965", "line": 500, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 500, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "1966", "line": 501, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 501, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "1967", "line": 501, "column": 33, "nodeType": "1880", "messageId": "1881", "endLine": 501, "endColumn": 58}, {"ruleId": "1878", "severity": 1, "message": "1968", "line": 504, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 504, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1969", "line": 504, "column": 19, "nodeType": "1880", "messageId": "1881", "endLine": 504, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "1970", "line": 505, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 505, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1971", "line": 505, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 505, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "1972", "line": 506, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 506, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "1973", "line": 506, "column": 21, "nodeType": "1880", "messageId": "1881", "endLine": 506, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "1974", "line": 515, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 515, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "1975", "line": 516, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 516, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1976", "line": 522, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 522, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "1977", "line": 526, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 526, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "1978", "line": 526, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 526, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "1979", "line": 529, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 529, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1980", "line": 529, "column": 20, "nodeType": "1880", "messageId": "1881", "endLine": 529, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "1982", "line": 569, "column": 5, "nodeType": "1983", "endLine": 569, "endColumn": 27, "suggestions": "1984"}, {"ruleId": "1878", "severity": 1, "message": "1985", "line": 579, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 579, "endColumn": 6}, {"ruleId": "1878", "severity": 1, "message": "1986", "line": 580, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 580, "endColumn": 7}, {"ruleId": "1878", "severity": 1, "message": "1987", "line": 581, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 581, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1988", "line": 583, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 583, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "1989", "line": 584, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 584, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "1990", "line": 589, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 589, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1991", "line": 590, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 590, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "1992", "line": 625, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 625, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1993", "line": 626, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 626, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1994", "line": 627, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 627, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1995", "line": 635, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 635, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "1996", "line": 637, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 637, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1997", "line": 638, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 638, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1998", "line": 639, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 639, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1999", "line": 640, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 640, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2000", "line": 645, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 645, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2001", "line": 647, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 647, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2002", "line": 649, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 649, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2003", "line": 659, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 659, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2004", "line": 660, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 660, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2005", "line": 663, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 663, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2006", "line": 667, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 667, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2007", "line": 669, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 669, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2008", "line": 670, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 670, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2009", "line": 672, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 672, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2010", "line": 679, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 679, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2011", "line": 680, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 680, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2012", "line": 685, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 685, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2013", "line": 686, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 686, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2014", "line": 687, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 687, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2015", "line": 697, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 697, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2016", "line": 701, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 701, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2017", "line": 705, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 705, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2018", "line": 707, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 707, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2019", "line": 709, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 709, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2020", "line": 710, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 710, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2021", "line": 715, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 715, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2022", "line": 716, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 716, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2023", "line": 727, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 727, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2024", "line": 734, "column": 18, "nodeType": "1880", "messageId": "1881", "endLine": 734, "endColumn": 37}, {"ruleId": "1878", "severity": 1, "message": "2025", "line": 735, "column": 18, "nodeType": "1880", "messageId": "1881", "endLine": 735, "endColumn": 37}, {"ruleId": "1878", "severity": 1, "message": "2026", "line": 739, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 739, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2027", "line": 751, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 751, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2028", "line": 776, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 776, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2029", "line": 787, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 787, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2030", "line": 792, "column": 25, "nodeType": "1880", "messageId": "1881", "endLine": 792, "endColumn": 42}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 797, "column": 22, "nodeType": "2033", "messageId": "2034", "endLine": 797, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2035", "line": 883, "column": 5, "nodeType": "1983", "endLine": 883, "endColumn": 46, "suggestions": "2036"}, {"ruleId": "1981", "severity": 1, "message": "2037", "line": 883, "column": 6, "nodeType": "2038", "endLine": 883, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2039", "line": 901, "column": 5, "nodeType": "1983", "endLine": 901, "endColumn": 18, "suggestions": "2040"}, {"ruleId": "1878", "severity": 1, "message": "2041", "line": 903, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 903, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2042", "line": 904, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 904, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2043", "line": 925, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 925, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2044", "line": 949, "column": 8, "nodeType": "2045", "endLine": 951, "endColumn": 3}, {"ruleId": "1981", "severity": 1, "message": "2046", "line": 986, "column": 5, "nodeType": "1983", "endLine": 994, "endColumn": 3, "suggestions": "2047"}, {"ruleId": "1981", "severity": 1, "message": "2048", "line": 1022, "column": 5, "nodeType": "1983", "endLine": 1045, "endColumn": 3, "suggestions": "2049"}, {"ruleId": "1981", "severity": 1, "message": "2050", "line": 1163, "column": 5, "nodeType": "1983", "endLine": 1163, "endColumn": 39, "suggestions": "2051"}, {"ruleId": "1878", "severity": 1, "message": "2052", "line": 1280, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 1280, "endColumn": 24}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 1372, "column": 25, "nodeType": "2033", "messageId": "2034", "endLine": 1372, "endColumn": 27}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 1379, "column": 25, "nodeType": "2033", "messageId": "2034", "endLine": 1379, "endColumn": 27}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 1379, "column": 53, "nodeType": "2033", "messageId": "2034", "endLine": 1379, "endColumn": 55}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 1382, "column": 26, "nodeType": "2033", "messageId": "2034", "endLine": 1382, "endColumn": 28}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 1382, "column": 58, "nodeType": "2033", "messageId": "2034", "endLine": 1382, "endColumn": 60}, {"ruleId": "1878", "severity": 1, "message": "2054", "line": 1513, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 1513, "endColumn": 33}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 1590, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 1590, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2055", "line": 1737, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 1737, "endColumn": 30}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 1999, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 1999, "endColumn": 21}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 2171, "column": 25, "nodeType": "2033", "messageId": "2034", "endLine": 2171, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2056", "line": 2203, "column": 5, "nodeType": "1983", "endLine": 2203, "endColumn": 18, "suggestions": "2057"}, {"ruleId": "1878", "severity": 1, "message": "2058", "line": 2260, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 2260, "endColumn": 36}, {"ruleId": "1878", "severity": 1, "message": "2059", "line": 2267, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 2267, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2060", "line": 2270, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 2270, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2061", "line": 2270, "column": 29, "nodeType": "1880", "messageId": "1881", "endLine": 2270, "endColumn": 48}, {"ruleId": "1878", "severity": 1, "message": "2062", "line": 2662, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 2662, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2063", "line": 2697, "column": 5, "nodeType": "1983", "endLine": 2697, "endColumn": 38, "suggestions": "2064"}, {"ruleId": "1878", "severity": 1, "message": "2065", "line": 2714, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 2714, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2066", "line": 2748, "column": 6, "nodeType": "1880", "messageId": "1881", "endLine": 2748, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2067", "line": 3132, "column": 4, "nodeType": "1983", "endLine": 3132, "endColumn": 18, "suggestions": "2068"}, {"ruleId": "1878", "severity": 1, "message": "2069", "line": 3476, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3476, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2070", "line": 3550, "column": 16, "nodeType": "2038", "endLine": 3550, "endColumn": 37}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 3551, "column": 56, "nodeType": "2033", "messageId": "2034", "endLine": 3551, "endColumn": 58}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 3555, "column": 49, "nodeType": "2033", "messageId": "2034", "endLine": 3555, "endColumn": 51}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 3559, "column": 50, "nodeType": "2033", "messageId": "2034", "endLine": 3559, "endColumn": 52}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 3565, "column": 51, "nodeType": "2033", "messageId": "2034", "endLine": 3565, "endColumn": 53}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 3572, "column": 51, "nodeType": "2033", "messageId": "2034", "endLine": 3572, "endColumn": 53}, {"ruleId": "1878", "severity": 1, "message": "2071", "line": 3795, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3795, "endColumn": 23}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 3803, "column": 30, "nodeType": "2033", "messageId": "2034", "endLine": 3803, "endColumn": 32}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 3816, "column": 39, "nodeType": "2033", "messageId": "2034", "endLine": 3816, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2072", "line": 3830, "column": 5, "nodeType": "1983", "endLine": 3830, "endColumn": 33, "suggestions": "2073"}, {"ruleId": "1878", "severity": 1, "message": "2074", "line": 3834, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 3834, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2075", "line": 3834, "column": 30, "nodeType": "1880", "messageId": "1881", "endLine": 3834, "endColumn": 52}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 3932, "column": 55, "nodeType": "2033", "messageId": "2034", "endLine": 3932, "endColumn": 57}, {"ruleId": "1878", "severity": 1, "message": "2076", "line": 3951, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3951, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2077", "line": 3953, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 3953, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2078", "line": 3957, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3957, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2079", "line": 3976, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 3976, "endColumn": 26}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 4002, "column": 66, "nodeType": "2033", "messageId": "2034", "endLine": 4002, "endColumn": 68}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 4009, "column": 5, "nodeType": "1983", "endLine": 4016, "endColumn": 3, "suggestions": "2081"}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 4250, "column": 17, "nodeType": "2033", "messageId": "2034", "endLine": 4250, "endColumn": 19}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 4505, "column": 21, "nodeType": "2033", "messageId": "2034", "endLine": 4505, "endColumn": 23}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 4513, "column": 21, "nodeType": "2033", "messageId": "2034", "endLine": 4513, "endColumn": 23}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 4526, "column": 15, "nodeType": "2033", "messageId": "2034", "endLine": 4526, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2082", "line": 4823, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 4823, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2083", "line": 4834, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 4834, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2084", "line": 4835, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 4835, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2085", "line": 4841, "column": 5, "nodeType": "1983", "endLine": 4841, "endColumn": 62, "suggestions": "2086"}, {"ruleId": "1981", "severity": 1, "message": "2037", "line": 4841, "column": 6, "nodeType": "2087", "endLine": 4841, "endColumn": 48}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 4864, "column": 25, "nodeType": "2033", "messageId": "2034", "endLine": 4864, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2088", "line": 4868, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 4868, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2089", "line": 4891, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 4891, "endColumn": 23}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 4966, "column": 25, "nodeType": "2033", "messageId": "2034", "endLine": 4966, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2090", "line": 4999, "column": 5, "nodeType": "1983", "endLine": 4999, "endColumn": 22, "suggestions": "2091"}, {"ruleId": "1878", "severity": 1, "message": "2092", "line": 5001, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 5001, "endColumn": 18}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 5003, "column": 40, "nodeType": "2033", "messageId": "2034", "endLine": 5003, "endColumn": 42}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 5068, "column": 69, "nodeType": "2033", "messageId": "2034", "endLine": 5068, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2093", "line": 5118, "column": 12, "nodeType": "1880", "messageId": "1881", "endLine": 5118, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2094", "line": 5119, "column": 12, "nodeType": "1880", "messageId": "1881", "endLine": 5119, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2095", "line": 5149, "column": 5, "nodeType": "1983", "endLine": 5149, "endColumn": 38, "suggestions": "2096"}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 5152, "column": 40, "nodeType": "2033", "messageId": "2034", "endLine": 5152, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2093", "line": 5158, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5158, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2094", "line": 5159, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5159, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2097", "line": 5165, "column": 5, "nodeType": "1983", "endLine": 5165, "endColumn": 106, "suggestions": "2098"}, {"ruleId": "1981", "severity": 1, "message": "2099", "line": 5278, "column": 5, "nodeType": "1983", "endLine": 5278, "endColumn": 17, "suggestions": "2100"}, {"ruleId": "1981", "severity": 1, "message": "2101", "line": 5294, "column": 5, "nodeType": "1983", "endLine": 5294, "endColumn": 78, "suggestions": "2102"}, {"ruleId": "1878", "severity": 1, "message": "2103", "line": 5297, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 5297, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2104", "line": 5310, "column": 8, "nodeType": "1983", "endLine": 5310, "endColumn": 15, "suggestions": "2105"}, {"ruleId": "2106", "severity": 1, "message": "2107", "line": 5907, "column": 80, "nodeType": "2108", "messageId": "2109", "endLine": 5907, "endColumn": 81, "suggestions": "2110"}, {"ruleId": "1878", "severity": 1, "message": "2111", "line": 6112, "column": 25, "nodeType": "1880", "messageId": "1881", "endLine": 6112, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2112", "line": 2, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2113", "line": 7, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2114", "line": 7, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2115", "line": 98, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 98, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2116", "line": 103, "column": 6, "nodeType": "1983", "endLine": 103, "endColumn": 8, "suggestions": "2117"}, {"ruleId": "1878", "severity": 1, "message": "2118", "line": 148, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 148, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2119", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2120", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2121", "line": 3, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2122", "line": 8, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2123", "line": 9, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2124", "line": 13, "column": 24, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 46}, {"ruleId": "2125", "severity": 1, "message": "2126", "line": 2400, "column": 5, "nodeType": "2127", "messageId": "2034", "endLine": 2400, "endColumn": 17}, {"ruleId": "2125", "severity": 1, "message": "2128", "line": 2401, "column": 5, "nodeType": "2127", "messageId": "2034", "endLine": 2401, "endColumn": 20}, {"ruleId": "2125", "severity": 1, "message": "2129", "line": 2732, "column": 5, "nodeType": "2127", "messageId": "2034", "endLine": 2732, "endColumn": 24}, {"ruleId": "2125", "severity": 1, "message": "2130", "line": 2909, "column": 5, "nodeType": "2127", "messageId": "2034", "endLine": 2909, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2131", "line": 3613, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 3613, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2131", "line": 3812, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 3812, "endColumn": 28}, {"ruleId": "2125", "severity": 1, "message": "2132", "line": 5247, "column": 5, "nodeType": "2127", "messageId": "2034", "endLine": 5247, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2133", "line": 5310, "column": 14, "nodeType": "1880", "messageId": "1881", "endLine": 5310, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2134", "line": 6401, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 6401, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2134", "line": 6427, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 6427, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2134", "line": 6433, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 6433, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2134", "line": 6448, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 6448, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2133", "line": 7225, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 7225, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2133", "line": 7469, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 7469, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2133", "line": 7640, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 7640, "endColumn": 16}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 8305, "column": 66, "nodeType": "2033", "messageId": "2034", "endLine": 8305, "endColumn": 68}, {"ruleId": "1878", "severity": 1, "message": "2135", "line": 70, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 70, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2136", "line": 1, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2137", "line": 2, "column": 44, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 56}, {"ruleId": "1878", "severity": 1, "message": "2138", "line": 18, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2139", "line": 19, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 7}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 20, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 20, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2141", "line": 21, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 21, "endColumn": 7}, {"ruleId": "1878", "severity": 1, "message": "2142", "line": 24, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2143", "line": 25, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2144", "line": 26, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2145", "line": 31, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 31, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2146", "line": 38, "column": 49, "nodeType": "1880", "messageId": "1881", "endLine": 38, "endColumn": 55}, {"ruleId": "1878", "severity": 1, "message": "2147", "line": 38, "column": 63, "nodeType": "1880", "messageId": "1881", "endLine": 38, "endColumn": 70}, {"ruleId": "1878", "severity": 1, "message": "2148", "line": 46, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 46, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2149", "line": 48, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 48, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2150", "line": 94, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 94, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2151", "line": 95, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 95, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2152", "line": 101, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 101, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2153", "line": 102, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 102, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2154", "line": 106, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 106, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2155", "line": 110, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 110, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2156", "line": 114, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 114, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2157", "line": 115, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 115, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2158", "line": 116, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 116, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2159", "line": 117, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 117, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2160", "line": 118, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 118, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2161", "line": 121, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 121, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2162", "line": 122, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 122, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2163", "line": 137, "column": 5, "nodeType": "1983", "endLine": 137, "endColumn": 15, "suggestions": "2164"}, {"ruleId": "1878", "severity": 1, "message": "2165", "line": 174, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 174, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2166", "line": 184, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 184, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2167", "line": 192, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 192, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2168", "line": 193, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 193, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2169", "line": 194, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 194, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2170", "line": 195, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 195, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2171", "line": 196, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 196, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2172", "line": 217, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 217, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2173", "line": 221, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 221, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2174", "line": 473, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 473, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2175", "line": 565, "column": 5, "nodeType": "1983", "endLine": 565, "endColumn": 60, "suggestions": "2176"}, {"ruleId": "1878", "severity": 1, "message": "2177", "line": 579, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 579, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2178", "line": 599, "column": 5, "nodeType": "1983", "endLine": 599, "endColumn": 60, "suggestions": "2179"}, {"ruleId": "1981", "severity": 1, "message": "2180", "line": 616, "column": 4, "nodeType": "1983", "endLine": 616, "endColumn": 6, "suggestions": "2181"}, {"ruleId": "1878", "severity": 1, "message": "2182", "line": 2, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2183", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 2, "column": 29, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 39}, {"ruleId": "1878", "severity": 1, "message": "1918", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "1919", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "1916", "line": 5, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "1917", "line": 5, "column": 46, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 55}, {"ruleId": "1878", "severity": 1, "message": "2185", "line": 6, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2186", "line": 6, "column": 24, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "1928", "line": 11, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2187", "line": 17, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2188", "line": 21, "column": 19, "nodeType": "1880", "messageId": "1881", "endLine": 21, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2189", "line": 24, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2190", "line": 25, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2191", "line": 26, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1961", "line": 35, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 35, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "1962", "line": 35, "column": 28, "nodeType": "1880", "messageId": "1881", "endLine": 35, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "1893", "line": 6, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2192", "line": 9, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 7}, {"ruleId": "1878", "severity": 1, "message": "2193", "line": 12, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2194", "line": 25, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 38}, {"ruleId": "1878", "severity": 1, "message": "2195", "line": 51, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2196", "line": 52, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 52, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2000", "line": 53, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 53, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2197", "line": 54, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 54, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2198", "line": 55, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 55, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2017", "line": 56, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 56, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2199", "line": 57, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 57, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2200", "line": 58, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 58, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2018", "line": 59, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 59, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2201", "line": 60, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2202", "line": 61, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 61, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2203", "line": 62, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 62, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2204", "line": 63, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 63, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2205", "line": 64, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 64, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2157", "line": 65, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 65, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2160", "line": 66, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 66, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2206", "line": 78, "column": 5, "nodeType": "1983", "endLine": 78, "endColumn": 7, "suggestions": "2207"}, {"ruleId": "1981", "severity": 1, "message": "2208", "line": 96, "column": 5, "nodeType": "1983", "endLine": 96, "endColumn": 28, "suggestions": "2209"}, {"ruleId": "1981", "severity": 1, "message": "2210", "line": 107, "column": 5, "nodeType": "1983", "endLine": 107, "endColumn": 48, "suggestions": "2211"}, {"ruleId": "1878", "severity": 1, "message": "2212", "line": 186, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 186, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2213", "line": 247, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 247, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2214", "line": 319, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 319, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2215", "line": 706, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 706, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2216", "line": 711, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 711, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2217", "line": 1, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2218", "line": 3, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2219", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1884", "line": 2, "column": 28, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2220", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2221", "line": 8, "column": 33, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2222", "line": 8, "column": 44, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 59}, {"ruleId": "1878", "severity": 1, "message": "2223", "line": 10, "column": 34, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 57}, {"ruleId": "1878", "severity": 1, "message": "2224", "line": 10, "column": 59, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 79}, {"ruleId": "1878", "severity": 1, "message": "2225", "line": 59, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 59, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2226", "line": 124, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 124, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1884", "line": 1, "column": 28, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2227", "line": 8, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2221", "line": 9, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2228", "line": 80, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 80, "endColumn": 58}, {"ruleId": "1981", "severity": 1, "message": "2229", "line": 86, "column": 8, "nodeType": "2045", "endLine": 90, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2230", "line": 86, "column": 8, "nodeType": "2045", "endLine": 90, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2231", "line": 92, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 92, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2232", "line": 92, "column": 25, "nodeType": "1880", "messageId": "1881", "endLine": 92, "endColumn": 42}, {"ruleId": "2233", "severity": 1, "message": "2234", "line": 113, "column": 113, "nodeType": "2235", "messageId": "2236", "endLine": 113, "endColumn": 397}, {"ruleId": "1981", "severity": 1, "message": "2237", "line": 154, "column": 5, "nodeType": "1983", "endLine": 154, "endColumn": 38, "suggestions": "2238"}, {"ruleId": "1981", "severity": 1, "message": "2037", "line": 154, "column": 6, "nodeType": "2033", "endLine": 154, "endColumn": 37}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 154, "column": 33, "nodeType": "2033", "messageId": "2034", "endLine": 154, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2225", "line": 156, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 156, "endColumn": 24}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 174, "column": 56, "nodeType": "2033", "messageId": "2034", "endLine": 174, "endColumn": 58}, {"ruleId": "1878", "severity": 1, "message": "2239", "line": 181, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 181, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2240", "line": 182, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 182, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2241", "line": 305, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 305, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2225", "line": 771, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 771, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2242", "line": 806, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 806, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2243", "line": 806, "column": 19, "nodeType": "1880", "messageId": "1881", "endLine": 806, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2244", "line": 807, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 807, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2245", "line": 807, "column": 22, "nodeType": "1880", "messageId": "1881", "endLine": 807, "endColumn": 36}, {"ruleId": "1878", "severity": 1, "message": "2060", "line": 808, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 808, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2061", "line": 808, "column": 29, "nodeType": "1880", "messageId": "1881", "endLine": 808, "endColumn": 48}, {"ruleId": "1878", "severity": 1, "message": "2246", "line": 809, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 809, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2247", "line": 809, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 809, "endColumn": 46}, {"ruleId": "1878", "severity": 1, "message": "1975", "line": 810, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 810, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2248", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2249", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2250", "line": 31, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 31, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2251", "line": 32, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 32, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2183", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "1888", "line": 2, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2187", "line": 4, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "1910", "line": 9, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 11, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 9}, {"ruleId": "1878", "severity": 1, "message": "2182", "line": 12, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2253", "line": 14, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 14, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2144", "line": 16, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 16, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2254", "line": 18, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2255", "line": 19, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2256", "line": 20, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 20, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2257", "line": 21, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 21, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2258", "line": 22, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 22, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2259", "line": 23, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 23, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2260", "line": 24, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2261", "line": 25, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 9}, {"ruleId": "1878", "severity": 1, "message": "1913", "line": 3, "column": 65, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 69}, {"ruleId": "1878", "severity": 1, "message": "2262", "line": 6, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2263", "line": 7, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2264", "line": 20, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 20, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2243", "line": 84, "column": 19, "nodeType": "1880", "messageId": "1881", "endLine": 84, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2265", "line": 85, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 85, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2266", "line": 85, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 85, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "2267", "line": 86, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 86, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2268", "line": 86, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 86, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "2269", "line": 90, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 90, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2270", "line": 90, "column": 45, "nodeType": "1880", "messageId": "1881", "endLine": 90, "endColumn": 62}, {"ruleId": "1878", "severity": 1, "message": "2271", "line": 93, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 93, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2272", "line": 94, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 94, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1968", "line": 95, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 95, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "1969", "line": 96, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 96, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1970", "line": 97, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 97, "endColumn": 9}, {"ruleId": "1878", "severity": 1, "message": "1971", "line": 98, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 98, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "1972", "line": 99, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 99, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1973", "line": 100, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 100, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2273", "line": 101, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 101, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2274", "line": 102, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 102, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2275", "line": 103, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 103, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "1987", "line": 104, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 104, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2276", "line": 105, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 105, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2277", "line": 107, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 107, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2278", "line": 108, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 108, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2279", "line": 115, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 115, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2280", "line": 116, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 116, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2281", "line": 118, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 118, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2282", "line": 119, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 119, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2283", "line": 120, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 120, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2284", "line": 121, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 121, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2285", "line": 122, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 122, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2286", "line": 123, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 123, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2287", "line": 132, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 132, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2288", "line": 137, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 137, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2151", "line": 139, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 139, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2158", "line": 140, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 140, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2289", "line": 141, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 141, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2290", "line": 144, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 144, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2156", "line": 145, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 145, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2291", "line": 146, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 146, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2292", "line": 170, "column": 5, "nodeType": "1983", "endLine": 170, "endColumn": 45, "suggestions": "2293"}, {"ruleId": "1878", "severity": 1, "message": "2294", "line": 221, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 221, "endColumn": 29}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 241, "column": 24, "nodeType": "2033", "messageId": "2034", "endLine": 241, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2295", "line": 315, "column": 7, "nodeType": "1983", "endLine": 315, "endColumn": 42, "suggestions": "2296"}, {"ruleId": "1878", "severity": 1, "message": "2297", "line": 339, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 339, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2298", "line": 340, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 340, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2299", "line": 488, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 488, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2300", "line": 491, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 491, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2301", "line": 500, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 500, "endColumn": 31}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 814, "column": 26, "nodeType": "2033", "messageId": "2034", "endLine": 814, "endColumn": 28}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 850, "column": 26, "nodeType": "2033", "messageId": "2034", "endLine": 850, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2302", "line": 1035, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1035, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2303", "line": 1039, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1039, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2304", "line": 1043, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1043, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2305", "line": 1047, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1047, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2306", "line": 1051, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1051, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2307", "line": 1055, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1055, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2308", "line": 1059, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1059, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2309", "line": 1063, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1063, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2310", "line": 5, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2311", "line": 13, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 47}, {"ruleId": "1878", "severity": 1, "message": "2312", "line": 15, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2313", "line": 79, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 79, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2314", "line": 81, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 81, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2315", "line": 82, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 82, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2316", "line": 83, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 83, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2317", "line": 84, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 84, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1995", "line": 88, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 88, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2318", "line": 89, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 89, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2319", "line": 91, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 91, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "1997", "line": 92, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 92, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1998", "line": 93, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 93, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1996", "line": 94, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 94, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2161", "line": 104, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 104, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2320", "line": 209, "column": 7, "nodeType": "1983", "endLine": 209, "endColumn": 9, "suggestions": "2321"}, {"ruleId": "1981", "severity": 1, "message": "2322", "line": 244, "column": 7, "nodeType": "1983", "endLine": 244, "endColumn": 29, "suggestions": "2323"}, {"ruleId": "1981", "severity": 1, "message": "2324", "line": 249, "column": 7, "nodeType": "1983", "endLine": 249, "endColumn": 18, "suggestions": "2325"}, {"ruleId": "1981", "severity": 1, "message": "2326", "line": 292, "column": 7, "nodeType": "1983", "endLine": 292, "endColumn": 72, "suggestions": "2327"}, {"ruleId": "1878", "severity": 1, "message": "2276", "line": 331, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 331, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2328", "line": 334, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 334, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "2329", "line": 463, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 463, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2330", "line": 4, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2331", "line": 6, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2332", "line": 7, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2333", "line": 7, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2334", "line": 8, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2335", "line": 9, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1997", "line": 13, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1998", "line": 14, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 14, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1996", "line": 15, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2336", "line": 17, "column": 21, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2337", "line": 18, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2338", "line": 18, "column": 33, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "2339", "line": 19, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2340", "line": 96, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 96, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2341", "line": 97, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 97, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2342", "line": 100, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 100, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2343", "line": 101, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 101, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2344", "line": 109, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 109, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2345", "line": 129, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 129, "endColumn": 16}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 176, "column": 45, "nodeType": "2033", "messageId": "2034", "endLine": 176, "endColumn": 47}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 176, "column": 104, "nodeType": "2033", "messageId": "2034", "endLine": 176, "endColumn": 106}, {"ruleId": "1878", "severity": 1, "message": "2145", "line": 2, "column": 60, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 73}, {"ruleId": "1878", "severity": 1, "message": "2330", "line": 3, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2346", "line": 8, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2347", "line": 9, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2348", "line": 133, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 133, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2349", "line": 134, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 134, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2350", "line": 135, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 135, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2351", "line": 135, "column": 30, "nodeType": "1880", "messageId": "1881", "endLine": 135, "endColumn": 52}, {"ruleId": "1878", "severity": 1, "message": "2161", "line": 137, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 137, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2352", "line": 163, "column": 8, "nodeType": "1983", "endLine": 163, "endColumn": 10, "suggestions": "2353"}, {"ruleId": "1878", "severity": 1, "message": "2354", "line": 299, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 299, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2355", "line": 342, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 342, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2356", "line": 343, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 343, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2357", "line": 344, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 344, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2358", "line": 346, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 346, "endColumn": 20}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 465, "column": 22, "nodeType": "2033", "messageId": "2034", "endLine": 465, "endColumn": 24}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 465, "column": 53, "nodeType": "2033", "messageId": "2034", "endLine": 465, "endColumn": 55}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 465, "column": 89, "nodeType": "2033", "messageId": "2034", "endLine": 465, "endColumn": 91}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 465, "column": 125, "nodeType": "2033", "messageId": "2034", "endLine": 465, "endColumn": 127}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 467, "column": 29, "nodeType": "2033", "messageId": "2034", "endLine": 467, "endColumn": 31}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 467, "column": 56, "nodeType": "2033", "messageId": "2034", "endLine": 467, "endColumn": 58}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 467, "column": 88, "nodeType": "2033", "messageId": "2034", "endLine": 467, "endColumn": 90}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 467, "column": 120, "nodeType": "2033", "messageId": "2034", "endLine": 467, "endColumn": 122}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 469, "column": 29, "nodeType": "2033", "messageId": "2034", "endLine": 469, "endColumn": 31}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 469, "column": 64, "nodeType": "2033", "messageId": "2034", "endLine": 469, "endColumn": 66}, {"ruleId": "1878", "severity": 1, "message": "2359", "line": 111, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 111, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2286", "line": 152, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 152, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2009", "line": 153, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 153, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2360", "line": 159, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 159, "endColumn": 23}, {"ruleId": "2361", "severity": 1, "message": "2362", "line": 225, "column": 25, "nodeType": "1880", "messageId": "2363", "endLine": 225, "endColumn": 34, "suggestions": "2364"}, {"ruleId": "1981", "severity": 1, "message": "2365", "line": 231, "column": 5, "nodeType": "1983", "endLine": 231, "endColumn": 12, "suggestions": "2366"}, {"ruleId": "1981", "severity": 1, "message": "2367", "line": 237, "column": 5, "nodeType": "1983", "endLine": 237, "endColumn": 21, "suggestions": "2368"}, {"ruleId": "1981", "severity": 1, "message": "2369", "line": 472, "column": 5, "nodeType": "1983", "endLine": 472, "endColumn": 70, "suggestions": "2370"}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 547, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 547, "endColumn": 21}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 548, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 548, "endColumn": 21}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 549, "column": 24, "nodeType": "2033", "messageId": "2034", "endLine": 549, "endColumn": 26}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 550, "column": 24, "nodeType": "2033", "messageId": "2034", "endLine": 550, "endColumn": 26}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 554, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 554, "endColumn": 21}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 555, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 555, "endColumn": 21}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 556, "column": 24, "nodeType": "2033", "messageId": "2034", "endLine": 556, "endColumn": 26}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 557, "column": 24, "nodeType": "2033", "messageId": "2034", "endLine": 557, "endColumn": 26}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 561, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 561, "endColumn": 21}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 562, "column": 24, "nodeType": "2033", "messageId": "2034", "endLine": 562, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2371", "line": 582, "column": 5, "nodeType": "1983", "endLine": 582, "endColumn": 64, "suggestions": "2372"}, {"ruleId": "1981", "severity": 1, "message": "2037", "line": 582, "column": 6, "nodeType": "2087", "endLine": 582, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2373", "line": 591, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 591, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2374", "line": 605, "column": 5, "nodeType": "1983", "endLine": 605, "endColumn": 47, "suggestions": "2375"}, {"ruleId": "1878", "severity": 1, "message": "2373", "line": 614, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 614, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2374", "line": 627, "column": 5, "nodeType": "1983", "endLine": 627, "endColumn": 47, "suggestions": "2376"}, {"ruleId": "1981", "severity": 1, "message": "2377", "line": 1021, "column": 17, "nodeType": "1880", "endLine": 1021, "endColumn": 32}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 1227, "column": 43, "nodeType": "2033", "messageId": "2034", "endLine": 1227, "endColumn": 45}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 1232, "column": 78, "nodeType": "2033", "messageId": "2034", "endLine": 1232, "endColumn": 80}, {"ruleId": "1878", "severity": 1, "message": "2378", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2379", "line": 2, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2380", "line": 2, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2121", "line": 3, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2381", "line": 11, "column": 62, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 67}, {"ruleId": "1878", "severity": 1, "message": "2204", "line": 25, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2160", "line": 28, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 28, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2382", "line": 31, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 31, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2383", "line": 144, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 144, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2384", "line": 145, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 145, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2385", "line": 1, "column": 28, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2182", "line": 5, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 5}, {"ruleId": "1878", "severity": 1, "message": "2386", "line": 6, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2387", "line": 10, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2388", "line": 12, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 13, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2389", "line": 17, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2227", "line": 19, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2390", "line": 33, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 33, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2244", "line": 33, "column": 33, "nodeType": "1880", "messageId": "1881", "endLine": 33, "endColumn": 44}, {"ruleId": "2391", "severity": 1, "message": "2392", "line": 95, "column": 2, "nodeType": "2393", "messageId": "2394", "endLine": 111, "endColumn": 4}, {"ruleId": "1878", "severity": 1, "message": "2395", "line": 132, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 132, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2396", "line": 135, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 135, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2274", "line": 136, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 136, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1987", "line": 137, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 137, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2397", "line": 139, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 139, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2271", "line": 140, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 140, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2398", "line": 141, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 141, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2399", "line": 144, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 144, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2400", "line": 145, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 145, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2401", "line": 146, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 146, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2402", "line": 147, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 147, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2403", "line": 148, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 148, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2404", "line": 149, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 149, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2281", "line": 150, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 150, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2280", "line": 151, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 151, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2279", "line": 152, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 152, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2405", "line": 155, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 155, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2406", "line": 158, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 158, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2407", "line": 159, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 159, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2408", "line": 160, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 160, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2197", "line": 161, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 161, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2155", "line": 162, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 162, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2409", "line": 165, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 165, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2410", "line": 166, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 166, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2360", "line": 168, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 168, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2411", "line": 173, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 173, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2009", "line": 174, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 174, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2412", "line": 185, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 185, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2413", "line": 185, "column": 25, "nodeType": "1880", "messageId": "1881", "endLine": 185, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2414", "line": 187, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 187, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2415", "line": 187, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 187, "endColumn": 44}, {"ruleId": "2416", "severity": 1, "message": "2417", "line": 349, "column": 5, "nodeType": "2418", "messageId": "2419", "endLine": 349, "endColumn": 52}, {"ruleId": "1878", "severity": 1, "message": "2420", "line": 504, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 504, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2421", "line": 511, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 511, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2422", "line": 668, "column": 5, "nodeType": "1983", "endLine": 668, "endColumn": 100, "suggestions": "2423"}, {"ruleId": "1981", "severity": 1, "message": "2424", "line": 775, "column": 5, "nodeType": "1983", "endLine": 775, "endColumn": 22, "suggestions": "2425"}, {"ruleId": "1878", "severity": 1, "message": "2426", "line": 876, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 876, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2378", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2427", "line": 1, "column": 28, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 36}, {"ruleId": "1878", "severity": 1, "message": "2428", "line": 4, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2195", "line": 14, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 14, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2201", "line": 15, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2196", "line": 16, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 16, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2160", "line": 17, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2205", "line": 20, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 20, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2429", "line": 26, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2427", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2430", "line": 2, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2121", "line": 2, "column": 19, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 2, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 37}, {"ruleId": "1878", "severity": 1, "message": "2182", "line": 2, "column": 39, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2387", "line": 2, "column": 44, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 58}, {"ruleId": "1878", "severity": 1, "message": "2145", "line": 2, "column": 60, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 73}, {"ruleId": "1878", "severity": 1, "message": "2258", "line": 2, "column": 74, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 84}, {"ruleId": "1878", "severity": 1, "message": "2431", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2432", "line": 4, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2195", "line": 98, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 98, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2201", "line": 99, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 99, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2196", "line": 100, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 100, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2000", "line": 101, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 101, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2197", "line": 102, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 102, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2198", "line": 103, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 103, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2017", "line": 104, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 104, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2018", "line": 105, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 105, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2245", "line": 110, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 110, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2161", "line": 113, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 113, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2433", "line": 172, "column": 12, "nodeType": "1983", "endLine": 172, "endColumn": 35, "suggestions": "2434"}, {"ruleId": "1878", "severity": 1, "message": "1884", "line": 1, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2114", "line": 4, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2219", "line": 7, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "1926", "line": 7, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 37}, {"ruleId": "1878", "severity": 1, "message": "2435", "line": 43, "column": 21, "nodeType": "1880", "messageId": "1881", "endLine": 43, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2436", "line": 63, "column": 21, "nodeType": "2437", "endLine": 63, "endColumn": 111}, {"ruleId": "1878", "severity": 1, "message": "2438", "line": 2, "column": 25, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 39}, {"ruleId": "1878", "severity": 1, "message": "2439", "line": 4, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2113", "line": 5, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2440", "line": 11, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2441", "line": 1, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2442", "line": 2, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "1915", "line": 1, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2442", "line": 2, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2443", "line": 2, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 40}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 2, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2444", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2444", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2182", "line": 2, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 2, "column": 15, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2258", "line": 2, "column": 50, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 60}, {"ruleId": "1878", "severity": 1, "message": "2431", "line": 29, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 29, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2186", "line": 34, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 34, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2411", "line": 64, "column": 29, "nodeType": "1880", "messageId": "1881", "endLine": 64, "endColumn": 43}, {"ruleId": "1878", "severity": 1, "message": "2445", "line": 72, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 72, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2446", "line": 74, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 74, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2447", "line": 97, "column": 15, "nodeType": "1880", "messageId": "1881", "endLine": 97, "endColumn": 22}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 106, "column": 34, "nodeType": "2033", "messageId": "2034", "endLine": 106, "endColumn": 36}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 154, "column": 44, "nodeType": "2033", "messageId": "2034", "endLine": 154, "endColumn": 46}, {"ruleId": "1878", "severity": 1, "message": "2448", "line": 173, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 173, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2449", "line": 301, "column": 5, "nodeType": "1983", "endLine": 301, "endColumn": 50, "suggestions": "2450"}, {"ruleId": "1981", "severity": 1, "message": "2449", "line": 317, "column": 5, "nodeType": "1983", "endLine": 317, "endColumn": 18, "suggestions": "2451"}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 477, "column": 48, "nodeType": "2033", "messageId": "2034", "endLine": 477, "endColumn": 50}, {"ruleId": "1878", "severity": 1, "message": "2378", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2452", "line": 61, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 61, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2453", "line": 63, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 63, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2454", "line": 391, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 391, "endColumn": 45}, {"ruleId": "1878", "severity": 1, "message": "2455", "line": 2, "column": 44, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 53}, {"ruleId": "1878", "severity": 1, "message": "2456", "line": 4, "column": 46, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 65}, {"ruleId": "1878", "severity": 1, "message": "1908", "line": 4, "column": 67, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 75}, {"ruleId": "1878", "severity": 1, "message": "2431", "line": 7, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2457", "line": 8, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2195", "line": 30, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 30, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2458", "line": 31, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 31, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "1996", "line": 34, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 34, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2459", "line": 44, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 44, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2460", "line": 45, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 45, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2461", "line": 46, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 46, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2462", "line": 47, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 47, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2463", "line": 51, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2464", "line": 51, "column": 21, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2465", "line": 52, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 52, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2466", "line": 53, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 53, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2467", "line": 56, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 56, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2468", "line": 57, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 57, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2469", "line": 57, "column": 20, "nodeType": "1880", "messageId": "1881", "endLine": 57, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "2470", "line": 65, "column": 5, "nodeType": "1983", "endLine": 65, "endColumn": 7, "suggestions": "2471"}, {"ruleId": "1878", "severity": 1, "message": "2472", "line": 93, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 93, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2473", "line": 97, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 97, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2474", "line": 124, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 124, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2475", "line": 132, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 132, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2476", "line": 136, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 136, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2477", "line": 150, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 150, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2478", "line": 153, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 153, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "1884", "line": 5, "column": 28, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2479", "line": 8, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2231", "line": 85, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 85, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2480", "line": 98, "column": 6, "nodeType": "1983", "endLine": 98, "endColumn": 8, "suggestions": "2481"}, {"ruleId": "1981", "severity": 1, "message": "2482", "line": 121, "column": 6, "nodeType": "1983", "endLine": 121, "endColumn": 32, "suggestions": "2483"}, {"ruleId": "1981", "severity": 1, "message": "2237", "line": 125, "column": 6, "nodeType": "1983", "endLine": 125, "endColumn": 40, "suggestions": "2484"}, {"ruleId": "1981", "severity": 1, "message": "2037", "line": 125, "column": 7, "nodeType": "2033", "endLine": 125, "endColumn": 39}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 125, "column": 35, "nodeType": "2033", "messageId": "2034", "endLine": 125, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2485", "line": 148, "column": 6, "nodeType": "1983", "endLine": 148, "endColumn": 33, "suggestions": "2486"}, {"ruleId": "1981", "severity": 1, "message": "2037", "line": 148, "column": 7, "nodeType": "2038", "endLine": 148, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2487", "line": 156, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 156, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2488", "line": 2, "column": 14, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2489", "line": 16, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 16, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2490", "line": 19, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2491", "line": 22, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 22, "endColumn": 20}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 43, "column": 100, "nodeType": "2033", "messageId": "2034", "endLine": 43, "endColumn": 102}, {"ruleId": "1878", "severity": 1, "message": "2492", "line": 4, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2493", "line": 4, "column": 32, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 45}, {"ruleId": "1878", "severity": 1, "message": "2494", "line": 10, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2495", "line": 65, "column": 12, "nodeType": "1880", "messageId": "1881", "endLine": 65, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2496", "line": 65, "column": 24, "nodeType": "1880", "messageId": "1881", "endLine": 65, "endColumn": 37}, {"ruleId": "1878", "severity": 1, "message": "2497", "line": 78, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 78, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2498", "line": 78, "column": 24, "nodeType": "1880", "messageId": "1881", "endLine": 78, "endColumn": 39}, {"ruleId": "1981", "severity": 1, "message": "2499", "line": 120, "column": 6, "nodeType": "1983", "endLine": 120, "endColumn": 8, "suggestions": "2500"}, {"ruleId": "1878", "severity": 1, "message": "2501", "line": 157, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 157, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2502", "line": 280, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 280, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2503", "line": 296, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 296, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2504", "line": 461, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 461, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2505", "line": 462, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 462, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2506", "line": 467, "column": 3, "nodeType": "1983", "endLine": 467, "endColumn": 5, "suggestions": "2507"}, {"ruleId": "1878", "severity": 1, "message": "2508", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 2, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "1910", "line": 2, "column": 64, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 78}, {"ruleId": "1878", "severity": 1, "message": "2254", "line": 2, "column": 80, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2255", "line": 2, "column": 93, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 103}, {"ruleId": "1878", "severity": 1, "message": "2256", "line": 2, "column": 105, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 111}, {"ruleId": "1878", "severity": 1, "message": "2257", "line": 2, "column": 113, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 121}, {"ruleId": "1878", "severity": 1, "message": "2509", "line": 2, "column": 123, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 140}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 2, "column": 142, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 158}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 2, "column": 160, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 166}, {"ruleId": "1878", "severity": 1, "message": "2511", "line": 2, "column": 168, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 180}, {"ruleId": "1878", "severity": 1, "message": "2512", "line": 2, "column": 182, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 199}, {"ruleId": "1878", "severity": 1, "message": "2513", "line": 4, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 49}, {"ruleId": "1878", "severity": 1, "message": "2514", "line": 4, "column": 51, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2515", "line": 4, "column": 73, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2516", "line": 5, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2517", "line": 13, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2518", "line": 14, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 14, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2519", "line": 15, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "1908", "line": 16, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 16, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2520", "line": 17, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 7}, {"ruleId": "1878", "severity": 1, "message": "2521", "line": 24, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2522", "line": 25, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2523", "line": 26, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2524", "line": 27, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 27, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2525", "line": 28, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 28, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2526", "line": 29, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 29, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2527", "line": 30, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 30, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2528", "line": 40, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 40, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2529", "line": 41, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 41, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2530", "line": 43, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 43, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2531", "line": 45, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 45, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2239", "line": 47, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 47, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2532", "line": 94, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 94, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2533", "line": 125, "column": 5, "nodeType": "1983", "endLine": 125, "endColumn": 7, "suggestions": "2534"}, {"ruleId": "1878", "severity": 1, "message": "2535", "line": 145, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 145, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2536", "line": 162, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 162, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2537", "line": 165, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 165, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2538", "line": 170, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 170, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2539", "line": 211, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 211, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2540", "line": 214, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 214, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2541", "line": 227, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 227, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2542", "line": 228, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 228, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2543", "line": 228, "column": 15, "nodeType": "1880", "messageId": "1881", "endLine": 228, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2544", "line": 229, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 229, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2545", "line": 229, "column": 20, "nodeType": "1880", "messageId": "1881", "endLine": 229, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "1963", "line": 245, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 245, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1964", "line": 245, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 245, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2546", "line": 247, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 247, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2547", "line": 261, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 261, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2548", "line": 281, "column": 4, "nodeType": "1983", "endLine": 281, "endColumn": 6, "suggestions": "2549"}, {"ruleId": "1878", "severity": 1, "message": "2547", "line": 334, "column": 12, "nodeType": "1880", "messageId": "1881", "endLine": 334, "endColumn": 38}, {"ruleId": "1878", "severity": 1, "message": "2550", "line": 347, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 347, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2551", "line": 347, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 347, "endColumn": 45}, {"ruleId": "1878", "severity": 1, "message": "2508", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 2, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "1910", "line": 2, "column": 64, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 78}, {"ruleId": "1878", "severity": 1, "message": "2254", "line": 2, "column": 80, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2255", "line": 2, "column": 93, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 103}, {"ruleId": "1878", "severity": 1, "message": "2256", "line": 2, "column": 105, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 111}, {"ruleId": "1878", "severity": 1, "message": "2257", "line": 2, "column": 113, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 121}, {"ruleId": "1878", "severity": 1, "message": "2509", "line": 2, "column": 123, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 140}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 2, "column": 142, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 158}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 2, "column": 160, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 166}, {"ruleId": "1878", "severity": 1, "message": "2259", "line": 2, "column": 168, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 175}, {"ruleId": "1878", "severity": 1, "message": "2513", "line": 4, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 49}, {"ruleId": "1878", "severity": 1, "message": "2514", "line": 4, "column": 51, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2515", "line": 4, "column": 73, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2516", "line": 5, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2517", "line": 8, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2518", "line": 9, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2519", "line": 10, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2520", "line": 11, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "1908", "line": 12, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2552", "line": 13, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2553", "line": 14, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 14, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2554", "line": 15, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2555", "line": 22, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 22, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2556", "line": 31, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 31, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2223", "line": 32, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 32, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2528", "line": 35, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 35, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2529", "line": 36, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 36, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2530", "line": 38, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 38, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2557", "line": 39, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 39, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2558", "line": 40, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 40, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2559", "line": 42, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 42, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2560", "line": 43, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 43, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2561", "line": 44, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 44, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2562", "line": 45, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 45, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2563", "line": 46, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 46, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2564", "line": 47, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 47, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2565", "line": 48, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 48, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2566", "line": 49, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 49, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2567", "line": 50, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 50, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2568", "line": 51, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2445", "line": 58, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 58, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2490", "line": 60, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2532", "line": 75, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 75, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2485", "line": 87, "column": 5, "nodeType": "1983", "endLine": 87, "endColumn": 45, "suggestions": "2569"}, {"ruleId": "1981", "severity": 1, "message": "2037", "line": 87, "column": 6, "nodeType": "2087", "endLine": 87, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "2570", "line": 106, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 106, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2571", "line": 106, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 106, "endColumn": 38}, {"ruleId": "1878", "severity": 1, "message": "2572", "line": 107, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 107, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2573", "line": 107, "column": 15, "nodeType": "1880", "messageId": "1881", "endLine": 107, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2574", "line": 108, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 108, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2154", "line": 109, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 109, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2575", "line": 109, "column": 18, "nodeType": "1880", "messageId": "1881", "endLine": 109, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2576", "line": 110, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 110, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2536", "line": 115, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 115, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2538", "line": 119, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 119, "endColumn": 25}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 134, "column": 11, "nodeType": "2033", "messageId": "2034", "endLine": 134, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2259", "line": 2, "column": 64, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2577", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2578", "line": 5, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2579", "line": 10, "column": 30, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 39}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 2, "column": 15, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2511", "line": 2, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 39}, {"ruleId": "1878", "severity": 1, "message": "2512", "line": 2, "column": 41, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 58}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 2, "column": 72, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 88}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 2, "column": 90, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 96}, {"ruleId": "1878", "severity": 1, "message": "2580", "line": 9, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 6, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2256", "line": 9, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2257", "line": 10, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2254", "line": 11, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2259", "line": 12, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 9}, {"ruleId": "1878", "severity": 1, "message": "2581", "line": 19, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2291", "line": 35, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 35, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2582", "line": 37, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 37, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2390", "line": 38, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 38, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2244", "line": 39, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 39, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2583", "line": 40, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 40, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2156", "line": 42, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 42, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2161", "line": 48, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 48, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2584", "line": 55, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 55, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2585", "line": 56, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 56, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2586", "line": 57, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 57, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2587", "line": 86, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 86, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2588", "line": 90, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 90, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2589", "line": 95, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 95, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2590", "line": 195, "column": 5, "nodeType": "1983", "endLine": 195, "endColumn": 30, "suggestions": "2591"}, {"ruleId": "1878", "severity": 1, "message": "2182", "line": 3, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 5}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 4, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2255", "line": 9, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2182", "line": 2, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 2, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 2, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2259", "line": 2, "column": 64, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2577", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 32}, {"ruleId": "1878", "severity": 1, "message": "2578", "line": 5, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2592", "line": 9, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2593", "line": 9, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2579", "line": 9, "column": 32, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 41}, {"ruleId": "1878", "severity": 1, "message": "2594", "line": 9, "column": 43, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 51}, {"ruleId": "1878", "severity": 1, "message": "2595", "line": 9, "column": 53, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 63}, {"ruleId": "1878", "severity": 1, "message": "2596", "line": 9, "column": 65, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 77}, {"ruleId": "1878", "severity": 1, "message": "2597", "line": 9, "column": 79, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 90}, {"ruleId": "1878", "severity": 1, "message": "2598", "line": 9, "column": 92, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 102}, {"ruleId": "1878", "severity": 1, "message": "2599", "line": 9, "column": 104, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 116}, {"ruleId": "1878", "severity": 1, "message": "2600", "line": 9, "column": 118, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 129}, {"ruleId": "1878", "severity": 1, "message": "2601", "line": 9, "column": 131, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 140}, {"ruleId": "1878", "severity": 1, "message": "2602", "line": 15, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2274", "line": 16, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 16, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2603", "line": 17, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2398", "line": 18, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2604", "line": 19, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "1987", "line": 20, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 20, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2397", "line": 23, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 23, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2605", "line": 24, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2606", "line": 25, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2607", "line": 26, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2608", "line": 27, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 27, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2609", "line": 28, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 28, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2610", "line": 29, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 29, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2611", "line": 30, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 30, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2445", "line": 34, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 34, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2532", "line": 62, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 62, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2588", "line": 82, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 82, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2612", "line": 83, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 83, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2508", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 3, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "1910", "line": 3, "column": 64, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 78}, {"ruleId": "1878", "severity": 1, "message": "2254", "line": 3, "column": 80, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2255", "line": 3, "column": 93, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 103}, {"ruleId": "1878", "severity": 1, "message": "2256", "line": 3, "column": 105, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 111}, {"ruleId": "1878", "severity": 1, "message": "2257", "line": 3, "column": 113, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 121}, {"ruleId": "1878", "severity": 1, "message": "2509", "line": 3, "column": 123, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 140}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 3, "column": 142, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 158}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 3, "column": 160, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 166}, {"ruleId": "1878", "severity": 1, "message": "2513", "line": 5, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 49}, {"ruleId": "1878", "severity": 1, "message": "2514", "line": 5, "column": 51, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2515", "line": 5, "column": 73, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2516", "line": 6, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2517", "line": 8, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2518", "line": 9, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2519", "line": 10, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2520", "line": 11, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2528", "line": 19, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2530", "line": 22, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 22, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2613", "line": 24, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2614", "line": 25, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2615", "line": 26, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2616", "line": 27, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 27, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2445", "line": 31, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 31, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2532", "line": 36, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 36, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2617", "line": 104, "column": 21, "nodeType": "1880", "messageId": "1881", "endLine": 104, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2618", "line": 105, "column": 24, "nodeType": "1880", "messageId": "1881", "endLine": 105, "endColumn": 40}, {"ruleId": "1878", "severity": 1, "message": "2535", "line": 120, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 120, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2536", "line": 154, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 154, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2619", "line": 157, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 157, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 3, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2259", "line": 3, "column": 56, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 63}, {"ruleId": "1878", "severity": 1, "message": "2157", "line": 13, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2271", "line": 14, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 14, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2272", "line": 15, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1968", "line": 16, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 16, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "1970", "line": 18, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 9}, {"ruleId": "1878", "severity": 1, "message": "1971", "line": 19, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "1972", "line": 20, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 20, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1973", "line": 21, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 21, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2273", "line": 22, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 22, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2274", "line": 23, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 23, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2275", "line": 24, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "1987", "line": 25, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2620", "line": 37, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 37, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2313", "line": 39, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 39, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2621", "line": 41, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 41, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2622", "line": 45, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 45, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2623", "line": 49, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 49, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2624", "line": 49, "column": 25, "nodeType": "1880", "messageId": "1881", "endLine": 49, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2625", "line": 50, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 50, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2626", "line": 50, "column": 21, "nodeType": "1880", "messageId": "1881", "endLine": 50, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2627", "line": 51, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2628", "line": 51, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "2629", "line": 52, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 52, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2630", "line": 52, "column": 30, "nodeType": "1880", "messageId": "1881", "endLine": 52, "endColumn": 52}, {"ruleId": "1878", "severity": 1, "message": "2631", "line": 53, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 53, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2632", "line": 53, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 53, "endColumn": 46}, {"ruleId": "1878", "severity": 1, "message": "2633", "line": 3, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2634", "line": 4, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2635", "line": 5, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2636", "line": 6, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2430", "line": 9, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 9}, {"ruleId": "1878", "severity": 1, "message": "2138", "line": 17, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2139", "line": 18, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 7}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 19, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2141", "line": 20, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 20, "endColumn": 7}, {"ruleId": "1878", "severity": 1, "message": "2142", "line": 23, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 23, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2143", "line": 24, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2144", "line": 25, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 25, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2259", "line": 26, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 9}, {"ruleId": "1878", "severity": 1, "message": "2331", "line": 43, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 43, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2637", "line": 44, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 44, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2638", "line": 50, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 50, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2639", "line": 51, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2640", "line": 53, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 53, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2641", "line": 54, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 54, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2642", "line": 55, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 55, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2643", "line": 56, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 56, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2157", "line": 58, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 58, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2195", "line": 59, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 59, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2458", "line": 60, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2151", "line": 62, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 62, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2152", "line": 68, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 68, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2153", "line": 69, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 69, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2154", "line": 75, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 75, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2644", "line": 77, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 77, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "1997", "line": 80, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 80, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1998", "line": 81, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 81, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1996", "line": 82, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 82, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2645", "line": 83, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 83, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2646", "line": 84, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 84, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2647", "line": 85, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 85, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2169", "line": 87, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 87, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2167", "line": 89, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 89, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2171", "line": 91, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 91, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "2648", "line": 92, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 92, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2162", "line": 94, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 94, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2274", "line": 101, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 101, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2273", "line": 101, "column": 22, "nodeType": "1880", "messageId": "1881", "endLine": 101, "endColumn": 36}, {"ruleId": "1878", "severity": 1, "message": "1987", "line": 102, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 102, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2275", "line": 102, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 102, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "2649", "line": 103, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 103, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2650", "line": 104, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 104, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2651", "line": 105, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 105, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2652", "line": 105, "column": 14, "nodeType": "1880", "messageId": "1881", "endLine": 105, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2165", "line": 106, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 106, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2653", "line": 106, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 106, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2358", "line": 107, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 107, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2654", "line": 107, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 107, "endColumn": 38}, {"ruleId": "1878", "severity": 1, "message": "2586", "line": 118, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 118, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2655", "line": 118, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 118, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2656", "line": 125, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 125, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2657", "line": 125, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 125, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2658", "line": 148, "column": 5, "nodeType": "1983", "endLine": 148, "endColumn": 60, "suggestions": "2659"}, {"ruleId": "1878", "severity": 1, "message": "2660", "line": 151, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 151, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2661", "line": 163, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 163, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2662", "line": 167, "column": 5, "nodeType": "1983", "endLine": 167, "endColumn": 60, "suggestions": "2663"}, {"ruleId": "1878", "severity": 1, "message": "2664", "line": 169, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 169, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2172", "line": 203, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 203, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2173", "line": 207, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 207, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2455", "line": 2, "column": 56, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 65}, {"ruleId": "1878", "severity": 1, "message": "2257", "line": 2, "column": 67, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 75}, {"ruleId": "1878", "severity": 1, "message": "2121", "line": 2, "column": 77, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 83}, {"ruleId": "1878", "severity": 1, "message": "2456", "line": 13, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2665", "line": 47, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 47, "endColumn": 48}, {"ruleId": "1878", "severity": 1, "message": "2354", "line": 59, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 59, "endColumn": 39}, {"ruleId": "1878", "severity": 1, "message": "2666", "line": 68, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 68, "endColumn": 41}, {"ruleId": "1878", "severity": 1, "message": "2667", "line": 74, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 74, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2508", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2378", "line": 1, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 47}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 2, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "1910", "line": 2, "column": 64, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 78}, {"ruleId": "1878", "severity": 1, "message": "2255", "line": 2, "column": 93, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 103}, {"ruleId": "1878", "severity": 1, "message": "2509", "line": 2, "column": 123, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 140}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 2, "column": 142, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 158}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 2, "column": 160, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 166}, {"ruleId": "1878", "severity": 1, "message": "2513", "line": 4, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 49}, {"ruleId": "1878", "severity": 1, "message": "2514", "line": 4, "column": 51, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2515", "line": 4, "column": 73, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2516", "line": 5, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2668", "line": 23, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 23, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2006", "line": 24, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 24, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2669", "line": 26, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2002", "line": 27, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 27, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2670", "line": 28, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 28, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2671", "line": 29, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 29, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2298", "line": 85, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 85, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2330", "line": 4, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "1997", "line": 11, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "1998", "line": 12, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "1996", "line": 13, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2336", "line": 21, "column": 21, "nodeType": "1880", "messageId": "1881", "endLine": 21, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2337", "line": 22, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 22, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2338", "line": 22, "column": 33, "nodeType": "1880", "messageId": "1881", "endLine": 22, "endColumn": 44}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 82, "column": 22, "nodeType": "2033", "messageId": "2034", "endLine": 82, "endColumn": 24}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 82, "column": 53, "nodeType": "2033", "messageId": "2034", "endLine": 82, "endColumn": 55}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 85, "column": 36, "nodeType": "2033", "messageId": "2034", "endLine": 85, "endColumn": 38}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 85, "column": 63, "nodeType": "2033", "messageId": "2034", "endLine": 85, "endColumn": 65}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 88, "column": 36, "nodeType": "2033", "messageId": "2034", "endLine": 88, "endColumn": 38}, {"ruleId": "1878", "severity": 1, "message": "2340", "line": 95, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 95, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2341", "line": 96, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 96, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2342", "line": 99, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 99, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2343", "line": 100, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 100, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2344", "line": 108, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 108, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2345", "line": 128, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 128, "endColumn": 16}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 268, "column": 45, "nodeType": "2033", "messageId": "2034", "endLine": 268, "endColumn": 47}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 268, "column": 104, "nodeType": "2033", "messageId": "2034", "endLine": 268, "endColumn": 106}, {"ruleId": "1878", "severity": 1, "message": "2672", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2386", "line": 4, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2455", "line": 8, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2673", "line": 15, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2674", "line": 15, "column": 30, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "2675", "line": 15, "column": 46, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 60}, {"ruleId": "1878", "severity": 1, "message": "2676", "line": 15, "column": 62, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 78}, {"ruleId": "1878", "severity": 1, "message": "2677", "line": 15, "column": 80, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 96}, {"ruleId": "1878", "severity": 1, "message": "2678", "line": 17, "column": 29, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2679", "line": 17, "column": 35, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 47}, {"ruleId": "1878", "severity": 1, "message": "2146", "line": 17, "column": 49, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 55}, {"ruleId": "1878", "severity": 1, "message": "2680", "line": 22, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 22, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2681", "line": 58, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 58, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2682", "line": 66, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 66, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2398", "line": 72, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 72, "endColumn": 8}, {"ruleId": "1878", "severity": 1, "message": "2397", "line": 73, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 73, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2606", "line": 74, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 74, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2274", "line": 75, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 75, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2683", "line": 76, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 76, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2405", "line": 77, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 77, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2684", "line": 78, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 78, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2406", "line": 79, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 79, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2401", "line": 80, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 80, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2685", "line": 81, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 81, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2608", "line": 82, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 82, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2155", "line": 83, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 83, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2150", "line": 84, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 84, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2686", "line": 87, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 87, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2687", "line": 89, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 89, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2156", "line": 91, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 91, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2161", "line": 93, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 93, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2688", "line": 166, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 166, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2689", "line": 169, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 169, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2690", "line": 179, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 179, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2691", "line": 332, "column": 5, "nodeType": "1983", "endLine": 332, "endColumn": 18, "suggestions": "2692"}, {"ruleId": "1878", "severity": 1, "message": "2408", "line": 599, "column": 33, "nodeType": "1880", "messageId": "1881", "endLine": 599, "endColumn": 47}, {"ruleId": "1878", "severity": 1, "message": "2244", "line": 599, "column": 49, "nodeType": "1880", "messageId": "1881", "endLine": 599, "endColumn": 60}, {"ruleId": "1878", "severity": 1, "message": "2197", "line": 599, "column": 62, "nodeType": "1880", "messageId": "1881", "endLine": 599, "endColumn": 67}, {"ruleId": "1878", "severity": 1, "message": "2155", "line": 599, "column": 69, "nodeType": "1880", "messageId": "1881", "endLine": 599, "endColumn": 85}, {"ruleId": "1981", "severity": 1, "message": "2693", "line": 927, "column": 3, "nodeType": "1983", "endLine": 927, "endColumn": 19, "suggestions": "2694"}, {"ruleId": "1878", "severity": 1, "message": "2695", "line": 999, "column": 15, "nodeType": "1880", "messageId": "1881", "endLine": 999, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2696", "line": 1008, "column": 15, "nodeType": "1880", "messageId": "1881", "endLine": 1008, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2388", "line": 11, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2330", "line": 14, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 14, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2697", "line": 67, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 67, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2698", "line": 180, "column": 86, "nodeType": "1880", "messageId": "1881", "endLine": 180, "endColumn": 101}, {"ruleId": "1878", "severity": 1, "message": "2406", "line": 184, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 184, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2699", "line": 563, "column": 5, "nodeType": "1983", "endLine": 563, "endColumn": 40, "suggestions": "2700"}, {"ruleId": "1981", "severity": 1, "message": "2701", "line": 586, "column": 6, "nodeType": "1983", "endLine": 586, "endColumn": 42, "suggestions": "2702"}, {"ruleId": "1981", "severity": 1, "message": "2703", "line": 600, "column": 6, "nodeType": "1983", "endLine": 600, "endColumn": 50, "suggestions": "2704"}, {"ruleId": "1981", "severity": 1, "message": "2705", "line": 877, "column": 5, "nodeType": "1983", "endLine": 877, "endColumn": 160, "suggestions": "2706"}, {"ruleId": "1981", "severity": 1, "message": "2707", "line": 945, "column": 5, "nodeType": "1983", "endLine": 945, "endColumn": 110, "suggestions": "2708"}, {"ruleId": "1981", "severity": 1, "message": "2709", "line": 975, "column": 5, "nodeType": "1983", "endLine": 975, "endColumn": 34, "suggestions": "2710"}, {"ruleId": "1981", "severity": 1, "message": "2711", "line": 993, "column": 5, "nodeType": "1983", "endLine": 993, "endColumn": 34, "suggestions": "2712"}, {"ruleId": "1981", "severity": 1, "message": "2711", "line": 1007, "column": 5, "nodeType": "1983", "endLine": 1007, "endColumn": 34, "suggestions": "2713"}, {"ruleId": "1981", "severity": 1, "message": "2711", "line": 1010, "column": 5, "nodeType": "1983", "endLine": 1010, "endColumn": 40, "suggestions": "2714"}, {"ruleId": "1878", "severity": 1, "message": "2715", "line": 1220, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 1220, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2716", "line": 1223, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 1223, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2259", "line": 2, "column": 64, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 2, "column": 73, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 79}, {"ruleId": "1878", "severity": 1, "message": "1928", "line": 15, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 9}, {"ruleId": "1878", "severity": 1, "message": "2717", "line": 18, "column": 48, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 76}, {"ruleId": "1878", "severity": 1, "message": "2718", "line": 18, "column": 78, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 85}, {"ruleId": "1878", "severity": 1, "message": "2264", "line": 20, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 20, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2719", "line": 52, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 52, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2720", "line": 54, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 54, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2721", "line": 59, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 59, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2687", "line": 59, "column": 18, "nodeType": "1880", "messageId": "1881", "endLine": 59, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2722", "line": 60, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 46}, {"ruleId": "1878", "severity": 1, "message": "1963", "line": 61, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 61, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "1964", "line": 61, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 61, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2664", "line": 85, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 85, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2538", "line": 92, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 92, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2723", "line": 183, "column": 5, "nodeType": "1983", "endLine": 183, "endColumn": 52, "suggestions": "2724"}, {"ruleId": "1981", "severity": 1, "message": "2037", "line": 183, "column": 6, "nodeType": "2087", "endLine": 183, "endColumn": 51}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 2, "column": 92, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 102}, {"ruleId": "1878", "severity": 1, "message": "2725", "line": 76, "column": 19, "nodeType": "1880", "messageId": "1881", "endLine": 76, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2430", "line": 2, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2258", "line": 2, "column": 36, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 46}, {"ruleId": "1878", "severity": 1, "message": "2455", "line": 2, "column": 48, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 57}, {"ruleId": "1878", "severity": 1, "message": "2257", "line": 2, "column": 59, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 67}, {"ruleId": "1878", "severity": 1, "message": "2121", "line": 2, "column": 69, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 75}, {"ruleId": "1878", "severity": 1, "message": "2259", "line": 2, "column": 77, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 84}, {"ruleId": "1878", "severity": 1, "message": "2726", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2727", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2728", "line": 8, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2729", "line": 9, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2508", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2427", "line": 1, "column": 29, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 37}, {"ruleId": "1878", "severity": 1, "message": "2378", "line": 1, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 47}, {"ruleId": "1878", "severity": 1, "message": "2183", "line": 1, "column": 49, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 59}, {"ruleId": "1878", "severity": 1, "message": "2385", "line": 1, "column": 61, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 67}, {"ruleId": "1878", "severity": 1, "message": "2455", "line": 2, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 36}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 2, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2121", "line": 2, "column": 56, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 62}, {"ruleId": "1878", "severity": 1, "message": "1910", "line": 2, "column": 64, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 78}, {"ruleId": "1878", "severity": 1, "message": "2254", "line": 2, "column": 80, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2255", "line": 2, "column": 93, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 103}, {"ruleId": "1878", "severity": 1, "message": "2256", "line": 2, "column": 105, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 111}, {"ruleId": "1878", "severity": 1, "message": "2257", "line": 2, "column": 113, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 121}, {"ruleId": "1878", "severity": 1, "message": "2509", "line": 2, "column": 123, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 140}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 2, "column": 142, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 158}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 2, "column": 160, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 166}, {"ruleId": "1878", "severity": 1, "message": "2431", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2219", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2513", "line": 4, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 49}, {"ruleId": "1878", "severity": 1, "message": "2514", "line": 4, "column": 51, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2515", "line": 4, "column": 73, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2516", "line": 5, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2517", "line": 7, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2518", "line": 8, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2519", "line": 9, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2520", "line": 10, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "1908", "line": 11, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2552", "line": 12, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2730", "line": 19, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2508", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 2, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "1910", "line": 2, "column": 64, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 78}, {"ruleId": "1878", "severity": 1, "message": "2255", "line": 2, "column": 93, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 103}, {"ruleId": "1878", "severity": 1, "message": "2509", "line": 2, "column": 123, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 140}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 2, "column": 142, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 158}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 2, "column": 160, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 166}, {"ruleId": "1878", "severity": 1, "message": "2143", "line": 2, "column": 177, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 193}, {"ruleId": "1878", "severity": 1, "message": "2513", "line": 4, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 49}, {"ruleId": "1878", "severity": 1, "message": "2514", "line": 4, "column": 51, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2515", "line": 4, "column": 73, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2516", "line": 5, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2517", "line": 7, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2518", "line": 8, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2519", "line": 9, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2520", "line": 10, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "1908", "line": 11, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2522", "line": 27, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 27, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2523", "line": 28, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 28, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2524", "line": 29, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 29, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2525", "line": 30, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 30, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2528", "line": 38, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 38, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2529", "line": 39, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 39, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2530", "line": 41, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 41, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2557", "line": 42, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 42, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2558", "line": 43, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 43, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2731", "line": 44, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 44, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2559", "line": 45, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 45, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2561", "line": 47, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 47, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2562", "line": 48, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 48, "endColumn": 33}, {"ruleId": "1878", "severity": 1, "message": "2563", "line": 49, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 49, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2565", "line": 51, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2566", "line": 52, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 52, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2567", "line": 53, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 53, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2568", "line": 54, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 54, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2445", "line": 58, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 58, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2732", "line": 152, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 152, "endColumn": 40}, {"ruleId": "1878", "severity": 1, "message": "2733", "line": 153, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 153, "endColumn": 41}, {"ruleId": "1878", "severity": 1, "message": "2734", "line": 154, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 154, "endColumn": 47}, {"ruleId": "1878", "severity": 1, "message": "1963", "line": 156, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 156, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2735", "line": 185, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 185, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2536", "line": 220, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 220, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2537", "line": 223, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 223, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2538", "line": 228, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 228, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2071", "line": 245, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 245, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2570", "line": 249, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 249, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2576", "line": 254, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 254, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2736", "line": 263, "column": 6, "nodeType": "1983", "endLine": 263, "endColumn": 8, "suggestions": "2737"}, {"ruleId": "1878", "severity": 1, "message": "2738", "line": 298, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 298, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2508", "line": 1, "column": 17, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2385", "line": 1, "column": 49, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 55}, {"ruleId": "1878", "severity": 1, "message": "2739", "line": 1, "column": 69, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 80}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 2, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2255", "line": 2, "column": 93, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 103}, {"ruleId": "1878", "severity": 1, "message": "2509", "line": 2, "column": 123, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 140}, {"ruleId": "1878", "severity": 1, "message": "2140", "line": 2, "column": 142, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 158}, {"ruleId": "1878", "severity": 1, "message": "2510", "line": 2, "column": 160, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 166}, {"ruleId": "1878", "severity": 1, "message": "2386", "line": 2, "column": 195, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 212}, {"ruleId": "1878", "severity": 1, "message": "2513", "line": 5, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 49}, {"ruleId": "1878", "severity": 1, "message": "2514", "line": 5, "column": 51, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 71}, {"ruleId": "1878", "severity": 1, "message": "2515", "line": 5, "column": 73, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 91}, {"ruleId": "1878", "severity": 1, "message": "2516", "line": 6, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2521", "line": 8, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2522", "line": 9, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2523", "line": 10, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2524", "line": 11, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 11, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2525", "line": 12, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2526", "line": 13, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2517", "line": 15, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2518", "line": 16, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 16, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2519", "line": 17, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 17, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2520", "line": 18, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 18, "endColumn": 10}, {"ruleId": "1878", "severity": 1, "message": "1908", "line": 19, "column": 5, "nodeType": "1880", "messageId": "1881", "endLine": 19, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2727", "line": 34, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 34, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2740", "line": 44, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 44, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2528", "line": 45, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 45, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2529", "line": 46, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 46, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2530", "line": 48, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 48, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2557", "line": 49, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 49, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2558", "line": 50, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 50, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2731", "line": 51, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2559", "line": 52, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 52, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2561", "line": 54, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 54, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2562", "line": 55, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 55, "endColumn": 38}, {"ruleId": "1878", "severity": 1, "message": "2563", "line": 56, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 56, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2565", "line": 58, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 58, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2566", "line": 59, "column": 6, "nodeType": "1880", "messageId": "1881", "endLine": 59, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2567", "line": 60, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2568", "line": 61, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 61, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2741", "line": 63, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 63, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2445", "line": 66, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 66, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2732", "line": 89, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 89, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2733", "line": 90, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 90, "endColumn": 43}, {"ruleId": "1878", "severity": 1, "message": "2734", "line": 91, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 91, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2742", "line": 112, "column": 5, "nodeType": "1983", "endLine": 112, "endColumn": 52, "suggestions": "2743"}, {"ruleId": "1878", "severity": 1, "message": "2536", "line": 117, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 117, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2537", "line": 120, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 120, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2538", "line": 125, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 125, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2744", "line": 135, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 135, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2071", "line": 175, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 175, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2745", "line": 183, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 183, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2736", "line": 188, "column": 4, "nodeType": "1983", "endLine": 188, "endColumn": 6, "suggestions": "2746"}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 193, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 193, "endColumn": 21}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 194, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 194, "endColumn": 21}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 194, "column": 39, "nodeType": "2033", "messageId": "2034", "endLine": 194, "endColumn": 41}, {"ruleId": "2031", "severity": 1, "message": "2032", "line": 213, "column": 19, "nodeType": "2033", "messageId": "2034", "endLine": 213, "endColumn": 21}, {"ruleId": "2031", "severity": 1, "message": "2053", "line": 226, "column": 20, "nodeType": "2033", "messageId": "2034", "endLine": 226, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2747", "line": 279, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 279, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "1963", "line": 307, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 307, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2735", "line": 334, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 334, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2748", "line": 371, "column": 4, "nodeType": "1983", "endLine": 371, "endColumn": 6, "suggestions": "2749"}, {"ruleId": "1878", "severity": 1, "message": "2182", "line": 2, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2455", "line": 2, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 36}, {"ruleId": "1878", "severity": 1, "message": "2252", "line": 2, "column": 38, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2274", "line": 9, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2273", "line": 9, "column": 22, "nodeType": "1880", "messageId": "1881", "endLine": 9, "endColumn": 36}, {"ruleId": "1878", "severity": 1, "message": "1987", "line": 10, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2275", "line": 10, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 10, "endColumn": 44}, {"ruleId": "1878", "severity": 1, "message": "2650", "line": 12, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2722", "line": 12, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 12, "endColumn": 46}, {"ruleId": "1878", "severity": 1, "message": "2651", "line": 13, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2652", "line": 13, "column": 14, "nodeType": "1880", "messageId": "1881", "endLine": 13, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2358", "line": 15, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 15, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2586", "line": 16, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 16, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2750", "line": 28, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 28, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2751", "line": 3, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 3, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2521", "line": 6, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 2, "column": 32, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 42}, {"ruleId": "1878", "severity": 1, "message": "2455", "line": 2, "column": 44, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 53}, {"ruleId": "1878", "severity": 1, "message": "2456", "line": 4, "column": 46, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 65}, {"ruleId": "1878", "severity": 1, "message": "1908", "line": 4, "column": 67, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 75}, {"ruleId": "1878", "severity": 1, "message": "2752", "line": 8, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2681", "line": 16, "column": 13, "nodeType": "1880", "messageId": "1881", "endLine": 16, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2753", "line": 31, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 31, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2288", "line": 33, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 33, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2754", "line": 43, "column": 6, "nodeType": "1880", "messageId": "1881", "endLine": 43, "endColumn": 34}, {"ruleId": "1878", "severity": 1, "message": "2755", "line": 85, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 85, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2477", "line": 95, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 95, "endColumn": 26}, {"ruleId": "1878", "severity": 1, "message": "2728", "line": 5, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2729", "line": 6, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2756", "line": 7, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2757", "line": 27, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 27, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2758", "line": 34, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 34, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2452", "line": 56, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 56, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2453", "line": 58, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 58, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2759", "line": 80, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 80, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2760", "line": 82, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 82, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2761", "line": 82, "column": 23, "nodeType": "1880", "messageId": "1881", "endLine": 82, "endColumn": 38}, {"ruleId": "1878", "severity": 1, "message": "2762", "line": 133, "column": 7, "nodeType": "1880", "messageId": "1881", "endLine": 133, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2763", "line": 290, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 290, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2764", "line": 344, "column": 5, "nodeType": "1983", "endLine": 344, "endColumn": 22, "suggestions": "2765"}, {"ruleId": "1878", "severity": 1, "message": "2672", "line": 1, "column": 58, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 67}, {"ruleId": "1878", "severity": 1, "message": "1884", "line": 1, "column": 75, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 82}, {"ruleId": "1878", "severity": 1, "message": "2430", "line": 2, "column": 15, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2184", "line": 2, "column": 33, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 43}, {"ruleId": "1878", "severity": 1, "message": "2680", "line": 4, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 4, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2137", "line": 5, "column": 41, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 53}, {"ruleId": "1878", "severity": 1, "message": "2678", "line": 6, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2146", "line": 6, "column": 16, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 22}, {"ruleId": "1878", "severity": 1, "message": "2766", "line": 6, "column": 24, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 29}, {"ruleId": "1878", "severity": 1, "message": "2767", "line": 6, "column": 31, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2310", "line": 6, "column": 37, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 47}, {"ruleId": "1878", "severity": 1, "message": "2679", "line": 6, "column": 49, "nodeType": "1880", "messageId": "1881", "endLine": 6, "endColumn": 61}, {"ruleId": "1878", "severity": 1, "message": "2431", "line": 7, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 7, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2727", "line": 8, "column": 8, "nodeType": "1880", "messageId": "1881", "endLine": 8, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2768", "line": 39, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 39, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2769", "line": 40, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 40, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2770", "line": 42, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 42, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2317", "line": 43, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 43, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2771", "line": 44, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 44, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2316", "line": 45, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 45, "endColumn": 16}, {"ruleId": "1878", "severity": 1, "message": "2315", "line": 46, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 46, "endColumn": 15}, {"ruleId": "1878", "severity": 1, "message": "2772", "line": 47, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 47, "endColumn": 12}, {"ruleId": "1878", "severity": 1, "message": "2313", "line": 49, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 49, "endColumn": 19}, {"ruleId": "1878", "severity": 1, "message": "2773", "line": 50, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 50, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2774", "line": 51, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 51, "endColumn": 25}, {"ruleId": "1878", "severity": 1, "message": "2395", "line": 54, "column": 4, "nodeType": "1880", "messageId": "1881", "endLine": 54, "endColumn": 11}, {"ruleId": "1878", "severity": 1, "message": "2242", "line": 58, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 58, "endColumn": 18}, {"ruleId": "1878", "severity": 1, "message": "2243", "line": 58, "column": 20, "nodeType": "1880", "messageId": "1881", "endLine": 58, "endColumn": 31}, {"ruleId": "1878", "severity": 1, "message": "2775", "line": 60, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2776", "line": 60, "column": 26, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 43}, {"ruleId": "1878", "severity": 1, "message": "2777", "line": 76, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 76, "endColumn": 30}, {"ruleId": "1878", "severity": 1, "message": "2778", "line": 84, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 84, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2779", "line": 117, "column": 6, "nodeType": "1983", "endLine": 117, "endColumn": 35, "suggestions": "2780"}, {"ruleId": "1878", "severity": 1, "message": "2142", "line": 2, "column": 2, "nodeType": "1880", "messageId": "1881", "endLine": 2, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2781", "line": 20, "column": 24, "nodeType": "1880", "messageId": "1881", "endLine": 20, "endColumn": 35}, {"ruleId": "1878", "severity": 1, "message": "2285", "line": 42, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 42, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "2390", "line": 43, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 43, "endColumn": 23}, {"ruleId": "1878", "severity": 1, "message": "2158", "line": 44, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 44, "endColumn": 28}, {"ruleId": "1878", "severity": 1, "message": "2782", "line": 46, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 46, "endColumn": 13}, {"ruleId": "1878", "severity": 1, "message": "2245", "line": 48, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 48, "endColumn": 17}, {"ruleId": "1878", "severity": 1, "message": "2288", "line": 49, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 49, "endColumn": 20}, {"ruleId": "1878", "severity": 1, "message": "2269", "line": 60, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2270", "line": 60, "column": 45, "nodeType": "1880", "messageId": "1881", "endLine": 60, "endColumn": 62}, {"ruleId": "1878", "severity": 1, "message": "2783", "line": 100, "column": 11, "nodeType": "1880", "messageId": "1881", "endLine": 100, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2784", "line": 103, "column": 12, "nodeType": "1880", "messageId": "1881", "endLine": 103, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2482", "line": 128, "column": 5, "nodeType": "1983", "endLine": 128, "endColumn": 155, "suggestions": "2785"}, {"ruleId": "1878", "severity": 1, "message": "2786", "line": 5, "column": 3, "nodeType": "1880", "messageId": "1881", "endLine": 5, "endColumn": 24}, {"ruleId": "1878", "severity": 1, "message": "2787", "line": 26, "column": 10, "nodeType": "1880", "messageId": "1881", "endLine": 26, "endColumn": 27}, {"ruleId": "1878", "severity": 1, "message": "2788", "line": 41, "column": 9, "nodeType": "1880", "messageId": "1881", "endLine": 41, "endColumn": 14}, {"ruleId": "1878", "severity": 1, "message": "2438", "line": 1, "column": 27, "nodeType": "1880", "messageId": "1881", "endLine": 1, "endColumn": 41}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'LoginUserInfo' is defined but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["2789"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2790"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2791"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "The 'initialState' object makes the dependencies of useEffect Hook (at line 1052) change on every render. To fix this, wrap the initialization of 'initialState' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2792"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2793"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2794"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'isShowIcon', 'resetALTKeywordForNewTooltip', 'setElementSelected', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2795"], "'handleElementSelectionToggle' is assigned a value but never used.", "'userInfoObj' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2796"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2797"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userType'. Either include it or remove the dependency array.", ["2798"], "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2799"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2800"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2801"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2802"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2803"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCurrentGuideId', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2804"], "React Hook useEffect has an unnecessary dependency: 'updatedGuideData'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData' aren't valid dependencies because mutating them doesn't re-render the component.", ["2805"], "'getAccountIdForUpdate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'deleteClicked', 'handleStepChange', and 'updateStepClicked'. Either include them or remove the dependency array.", ["2806"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2807", "2808"], "'selectedStepTitle' is assigned a value but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", ["2809"], "'signIn' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'defaultImages'. Either include it or remove the dependency array.", ["2810"], "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2811"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2812"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2813"], "'Box' is defined but never used.", "'useContext' is defined but never used.", "'Typography' is defined but never used.", "'AuthProvider' is defined but never used.", "'useAuth' is defined but never used.", "'AccountContext' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2814"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2815"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2816"], "'handleEnableAI' is assigned a value but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", "'response' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'useDrawerStore' is defined but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2817"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'overlayEnabled' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2818"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2819"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'setButtonProperty' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2820"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2821"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2822"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2823"], "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2824"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["2825"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["2826"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["2827"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["2828"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2829"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2830"], ["2831"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'count' is assigned a value but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2832"], "React Hook useEffect has missing dependencies: 'isALTKeywordEnabled', 'selectedTemplate', 'selectedTemplateTour', and 'setIsALTKeywordEnabled'. Either include them or remove the dependency array.", ["2833"], "'DotsStepper' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2834"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'AxiosResponse' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2835"], ["2836"], "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'isCurrentlyEditing' is assigned a value but never used.", "'TextField' is defined but never used.", "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2837"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2838"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2839"], ["2840"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2841"], "'toggleItemCompletion' is assigned a value but never used.", "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'accountId' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["2842"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["2843"], "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2844"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2845"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", ["2846"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'OverlaySettingsProps' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["2847"], "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'announcement<PERSON>son' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["2848"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2849"], "'handlePositionClick' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'translate' is assigned a value but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2850"], "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["2851"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["2852"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["2853"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["2854"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["2855"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["2856"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["2857"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["2858"], ["2859"], ["2860"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2861"], "'guideStatus' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2862"], "'handleMenuScroll' is assigned a value but never used.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["2863"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["2864"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["2865"], "'handleColorChange' is assigned a value but never used.", "'FolderIcon' is defined but never used.", "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["2866"], "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "'handleTooltipRTEValue' is assigned a value but never used.", "'anchorPosition' is assigned a value but never used.", "'setAnchorPosition' is assigned a value but never used.", "'preserveCaretPosition' is assigned a value but never used.", "'restoreCaretPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'boxRef' and 'textvaluess'. Either include them or remove the dependency array. Mutable values like 'boxRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["2867"], "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["2868"], "'getAvailableLanguages' is defined but never used.", "'getOrgLanguageKey' is defined but never used.", "'orgId' is assigned a value but never used.", {"desc": "2869", "fix": "2870"}, {"desc": "2871", "fix": "2872"}, {"desc": "2873", "fix": "2874"}, {"desc": "2875", "fix": "2876"}, {"desc": "2877", "fix": "2878"}, {"desc": "2879", "fix": "2880"}, {"desc": "2881", "fix": "2882"}, {"desc": "2883", "fix": "2884"}, {"desc": "2885", "fix": "2886"}, {"desc": "2887", "fix": "2888"}, {"desc": "2889", "fix": "2890"}, {"desc": "2891", "fix": "2892"}, {"desc": "2893", "fix": "2894"}, {"desc": "2895", "fix": "2896"}, {"desc": "2897", "fix": "2898"}, {"desc": "2899", "fix": "2900"}, {"desc": "2901", "fix": "2902"}, {"desc": "2903", "fix": "2904"}, {"messageId": "2905", "fix": "2906", "desc": "2907"}, {"messageId": "2908", "fix": "2909", "desc": "2910"}, {"desc": "2911", "fix": "2912"}, {"desc": "2913", "fix": "2914"}, {"desc": "2915", "fix": "2916"}, {"desc": "2917", "fix": "2918"}, {"desc": "2919", "fix": "2920"}, {"desc": "2921", "fix": "2922"}, {"desc": "2923", "fix": "2924"}, {"desc": "2925", "fix": "2926"}, {"desc": "2927", "fix": "2928"}, {"desc": "2929", "fix": "2930"}, {"desc": "2931", "fix": "2932"}, {"desc": "2933", "fix": "2934"}, {"desc": "2935", "fix": "2936"}, {"desc": "2937", "fix": "2938"}, {"desc": "2939", "fix": "2940"}, {"desc": "2941", "fix": "2942"}, {"messageId": "2943", "data": "2944", "fix": "2945", "desc": "2946"}, {"desc": "2947", "fix": "2948"}, {"desc": "2949", "fix": "2950"}, {"desc": "2951", "fix": "2952"}, {"desc": "2953", "fix": "2954"}, {"desc": "2955", "fix": "2956"}, {"desc": "2957", "fix": "2958"}, {"desc": "2959", "fix": "2960"}, {"desc": "2961", "fix": "2962"}, {"desc": "2963", "fix": "2964"}, {"desc": "2965", "fix": "2966"}, {"desc": "2967", "fix": "2968"}, {"desc": "2969", "fix": "2970"}, {"desc": "2971", "fix": "2972"}, {"desc": "2973", "fix": "2974"}, {"desc": "2927", "fix": "2975"}, {"desc": "2976", "fix": "2977"}, {"desc": "2978", "fix": "2979"}, {"desc": "2980", "fix": "2981"}, {"desc": "2982", "fix": "2983"}, {"desc": "2984", "fix": "2985"}, {"desc": "2976", "fix": "2986"}, {"desc": "2987", "fix": "2988"}, {"desc": "2989", "fix": "2990"}, {"desc": "2991", "fix": "2992"}, {"desc": "2993", "fix": "2994"}, {"desc": "2995", "fix": "2996"}, {"desc": "2997", "fix": "2998"}, {"desc": "2999", "fix": "3000"}, {"desc": "3001", "fix": "3002"}, {"desc": "3003", "fix": "3004"}, {"desc": "3005", "fix": "3006"}, {"desc": "3007", "fix": "3008"}, {"desc": "3009", "fix": "3010"}, {"desc": "3009", "fix": "3011"}, {"desc": "3012", "fix": "3013"}, {"desc": "3014", "fix": "3015"}, {"desc": "3016", "fix": "3017"}, {"desc": "3018", "fix": "3019"}, {"desc": "3016", "fix": "3020"}, {"desc": "3021", "fix": "3022"}, {"desc": "3023", "fix": "3024"}, {"desc": "3025", "fix": "3026"}, {"desc": "3027", "fix": "3028"}, "Update the dependencies array to be: []", {"range": "3029", "text": "3030"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "3031", "text": "3032"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "3033", "text": "3034"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "3035", "text": "3036"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "3037", "text": "3038"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3039", "text": "3040"}, "Update the dependencies array to be: [currentStep, elementSelected, handleClose, isShowIcon, resetALTKeywordForNewTooltip, setElementSelected, toolTipGuideMetaData]", {"range": "3041", "text": "3042"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3043", "text": "3044"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3045", "text": "3046"}, "Update the dependencies array to be: [isLoggedIn, organizationId, userType]", {"range": "3047", "text": "3048"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3049", "text": "3050"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3051", "text": "3052"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3053", "text": "3054"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3055", "text": "3056"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3057", "text": "3058"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3059", "text": "3060"}, "Update the dependencies array to be: [pendingWebTourModal, isTourPopupOpen, tourModalSource]", {"range": "3061", "text": "3062"}, "Update the dependencies array to be: [currentStep, deleteClicked, handleStepChange, steps, updateStepClicked]", {"range": "3063", "text": "3064"}, "removeEscape", {"range": "3065", "text": "3066"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3067", "text": "3068"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [loggedOut]", {"range": "3069", "text": "3070"}, "Update the dependencies array to be: [defaultImages, sections]", {"range": "3071", "text": "3072"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3073", "text": "3074"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3075", "text": "3076"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3077", "text": "3078"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3079", "text": "3080"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3081", "text": "3082"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3083", "text": "3084"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3085", "text": "3086"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3087", "text": "3088"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3089", "text": "3090"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3091", "text": "3092"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3093", "text": "3094"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3095", "text": "3096"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3097", "text": "3098"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3099", "text": "3100"}, "suggestString", {"type": "3101"}, {"range": "3102", "text": "3103"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3104", "text": "3105"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3106", "text": "3107"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3108", "text": "3109"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3110", "text": "3111"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3112", "text": "3113"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3114", "text": "3115"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", {"range": "3116", "text": "3117"}, "Update the dependencies array to be: [elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", {"range": "3118", "text": "3119"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3120", "text": "3121"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3122", "text": "3123"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3124", "text": "3125"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3126", "text": "3127"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3128", "text": "3129"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3130", "text": "3131"}, {"range": "3132", "text": "3086"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3133", "text": "3134"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3135", "text": "3136"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3137", "text": "3138"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3139", "text": "3140"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3141", "text": "3142"}, {"range": "3143", "text": "3134"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3144", "text": "3145"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3146", "text": "3147"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3148", "text": "3149"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3150", "text": "3151"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3152", "text": "3153"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3154", "text": "3155"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3156", "text": "3157"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3158", "text": "3159"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3160", "text": "3161"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3162", "text": "3163"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3164", "text": "3165"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3166", "text": "3167"}, {"range": "3168", "text": "3167"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3169", "text": "3170"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3171", "text": "3172"}, "Update the dependencies array to be: [fetchData]", {"range": "3173", "text": "3174"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3175", "text": "3176"}, {"range": "3177", "text": "3174"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3178", "text": "3179"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3180", "text": "3181"}, "Update the dependencies array to be: [rteBoxValue, boxRef, textvaluess]", {"range": "3182", "text": "3183"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3184", "text": "3185"}, [18210, 18232], "[]", [26521, 26562], "[fetchGuideDetails, hotspot, hotspotClicked]", [27044, 27057], "[designPopup, setDesignPopup]", [29781, 29928], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [30406, 30766], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [35374, 35408], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [70711, 70724], "[currentStep, elementSelected, handleClose, isShowIcon, resetALTKeywordForNewTooltip, setElementSelected, toolTipGuideMetaData]", [86739, 86772], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [101372, 101386], "[createWithAI, setIsUnSavedChanges, stepCreation]", [124488, 124516], "[isLoggedIn, organizationId, userType]", [130638, 130790], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [160479, 160536], "[currentGuide?.GuideStep, currentStep]", [165792, 165809], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [171741, 171774], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [172484, 172585], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [176685, 176697], "[isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [177117, 177190], "[pendingWebTourModal, isTourPopupOpen, tourModalSource]", [177759, 177766], "[currentStep, deleteClicked, handleStepChange, steps, updateStepClicked]", [198484, 198485], "", [198484, 198484], "\\", [4501, 4503], "[loggedOut]", [4056, 4066], "[defaultImages, sections]", [16939, 16994], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [18368, 18423], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [18868, 18870], "[selectedActions.value, targetURL]", [2347, 2349], "[isExtensionClosed, setIsExtensionClosed]", [3014, 3037], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3381, 3424], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [5960, 5993], "[checkpointslistData]", [5310, 5350], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10234, 10269], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [6153, 6155], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7218, 7240], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7401, 7412], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8963, 9028], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [4728, 4730], "[OverlayValue]", "undefined", [6583, 6592], "\"undefined\"", [6719, 6726], "[getElementPosition, xpath]", [6855, 6871], "[savedGuideData, xpath]", [14985, 15050], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [19065, 19124], "[currentStep, guideStep, setOpenTooltip]", [19978, 20020], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [20812, 20854], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [22789, 22884], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", [26752, 26769], "[elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [8894, 8939], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [9245, 9258], "[fetchAnnouncements, searchQuery]", [2651, 2653], "[setButtonProperty]", [3543, 3545], "[checkpointslistData, completedStatus]", [4387, 4413], "[selectedItem, activeItem, createWithAI, interactionData]", [4517, 4551], [5507, 5534], "[checklistGuideMetaData]", [4604, 4606], "[accountId, openSnackbar]", [17324, 17326], "[setElementSelected]", [4872, 4874], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9628, 9630], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3211, 3251], [6878, 6903], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [4631, 4686], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [6164, 6219], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [9769, 9782], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [26434, 26450], "[handleFocus, isRtlDirection]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [33472, 33501], "[currentStepData, currentStepIndex, handleNext]", [33978, 34007], "[currentStepData, currentUrl, updateTargetAndPosition]", [34419, 34448], [34507, 34542], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7898, 7945], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [7680, 7682], "[fetchData]", [4285, 4332], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6555, 6557], [12538, 12540], "[checklistCheckpointListProperties, icons]", [10130, 10147], "[setImageAnchorEl, tooltip.visible]", [3811, 3840], "[rteBoxV<PERSON>ue, boxRef, textvaluess]", [4246, 4396], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]"]