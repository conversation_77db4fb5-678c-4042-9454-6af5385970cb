import React, { useState, useEffect, useRef, forwardRef, useMemo } from "react";
import { Box, IconButton } from "@mui/material";
import JoditEditor from "jodit-react";
import useDrawerStore from "../../../store/drawerStore";
import { copyicon, deleteicon, settingsicon } from "../../../assets/icons/icons";
import { useTranslation } from 'react-i18next';

interface RTEsectionProps {
    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;
    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;
    isBanner: boolean;
    handleDeleteRTESection: (params: number) => void;
    index: number;
    onClone?: () => void;
    isCloneDisabled?: boolean;
}

const RTEsection: React.FC<RTEsectionProps> = forwardRef(
    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {
        const { t: translate } = useTranslation();
        const {
            rtesContainer,
            updateRTEContainer,
            setIsUnSavedChanges,
            cloneRTEContainer,
            clearRteDetails,
            selectedTemplate,
            selectedTemplateTour,
            announcementGuideMetaData,
            toolTipGuideMetaData,
            handleAnnouncementRTEValue,
            handleTooltipRTEValue,
            createWithAI,
            currentStep,
            ensureAnnouncementRTEContainer
        } = useDrawerStore();

        // Individual state management for each RTE
        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);
        const [toolbarVisible, setToolbarVisible] = useState<{[key: string]: boolean}>({});
        const contentRef = useRef<string>("");

        // Map to store individual refs for each RTE
        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());
        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());

        // Helper function to get or create editor ref for specific RTE
        const getEditorRef = (rteId: string) => {
            if (!editorRefs.current.has(rteId)) {
                const newRef = React.createRef();
                editorRefs.current.set(rteId, newRef);
                console.log(`Created new editor ref for RTE: ${rteId}`);
            }
            return editorRefs.current.get(rteId);
        };

        // Helper function to get or create container ref for specific RTE
        const getContainerRef = (rteId: string) => {
            if (!containerRefs.current.has(rteId)) {
                containerRefs.current.set(rteId, React.createRef());
            }
            return containerRefs.current.get(rteId);
        };

        // Handle clicks outside the editor - now works with individual RTEs
        useEffect(() => {
            const handleClickOutside = (event: MouseEvent) => {
                if (!editingRTEId) return; // No RTE is currently being edited

                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(".jodit-popup__content") !== null;
                const isInsideAltTextPopup = (event.target as HTMLElement).closest(".jodit-ui-input") !== null;
                const isInsidePopup = document.querySelector(".jodit-popup")?.contains(event.target as Node);
                const isInsideJoditPopup = document.querySelector(".jodit-wysiwyg")?.contains(event.target as Node);
                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(".jodit-dialog__panel")?.contains(event.target as Node);
                const isSelectionMarker = (event.target as HTMLElement).id.startsWith("jodit-selection_marker_");
                const isLinkPopup = document.querySelector(".jodit-ui-input__input")?.contains(event.target as Node);
                const isInsideToolbarButton = (event.target as HTMLElement).closest(".jodit-toolbar-button__button") !== null;
                const isInsertButton = (event.target as HTMLElement).closest("button[aria-pressed='false']") !== null;

                // Get the container ref for the currently editing RTE
                const currentContainerRef = getContainerRef(editingRTEId);

                // Check if the target is inside the currently editing RTE or related elements
                if (
                    currentContainerRef?.current &&
                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container
                    !isInsidePopup && // Click outside the popup
                    !isInsideJoditPopup && // Click outside the WYSIWYG editor
                    !isInsideWorkplacePopup && // Click outside the workplace popup
                    !isSelectionMarker && // Click outside selection markers
                    !isLinkPopup && // Click outside link input popup
                    !isInsideToolbarButton && // Click outside the toolbar button
                    !isInsertButton &&
                    !isInsideJoditPopupContent &&
                    !isInsideAltTextPopup
                ) {
                    setEditingRTEId(null); // Close the currently editing RTE
                }
            };

            document.addEventListener("mousedown", handleClickOutside);
            return () => document.removeEventListener("mousedown", handleClickOutside);
        }, [editingRTEId]);

        useEffect(() => {
            if (editingRTEId) {
                const editorRef = getEditorRef(editingRTEId);
                if (editorRef?.current) {
                    setTimeout(() => {
                        //(editorRef.current as any).editor.focus();
                    }, 50);
                }
            }
        }, [editingRTEId]);



        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {
            contentRef.current = newContent;

            // Check if this is an AI-created guide
            const isAIAnnouncement = createWithAI && (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement");
            const isAITour = createWithAI && selectedTemplate === "Tour";
            const isTourAnnouncement = isAITour && selectedTemplateTour === "Announcement";
            const isTourBanner = isAITour && selectedTemplateTour === "Banner";
            const isTourTooltip = isAITour && (selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Hotspot");

            console.log("RTEsection handleUpdate:", {
                createWithAI,
                selectedTemplate,
                selectedTemplateTour,
                isAIAnnouncement,
                isAITour,
                isTourBanner,
                containerId,
                newContent: newContent.substring(0, 50) + "..."
            });

            if (isAIAnnouncement) {
                const currentStepIndex = currentStep - 1;

                if (isTourAnnouncement) {
                    // For Tour+Announcement, use toolTipGuideMetaData
                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(
                        (container: any) => container.id === containerId && container.type === "rte"
                    );

                    if (tooltipContainer) {
                        // Use the tooltip-specific handler for tour announcements
                        handleTooltipRTEValue(containerId, newContent);
                    }
                } else {
                    // For pure Announcements, use announcementGuideMetaData
                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(
                        (container: any) => container.id === containerId && container.type === "rte"
                    );

                    if (announcementContainer) {
                        // Use the announcement-specific handler
                        handleAnnouncementRTEValue(containerId, newContent);
                    }
                }
            } else if (isAITour && (isTourBanner || isTourTooltip)) {
                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData
                const currentStepIndex = currentStep - 1;
                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(
                    (container: any) => container.id === containerId && container.type === "rte"
                );

                if (tooltipContainer) {
                    // Use the tooltip-specific handler for all tour step types
                    handleTooltipRTEValue(containerId, newContent);
                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);
                } else {
                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {
                        currentStepIndex,
                        containerId,
                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))
                    });
                }
            } else {
                // For non-AI content or other cases, use the regular RTE container system
                updateRTEContainer(containerId, rteId, newContent);
                console.log("Used updateRTEContainer for non-AI content");
            }

            setIsUnSavedChanges(true);
        };
        const handleCloneContainer = (containerId: string) => {
            // Check if cloning is disabled due to section limits
            if (isCloneDisabled) {
                return; // Don't clone if limit is reached
            }

            // Call the clone function from the store
            cloneRTEContainer(containerId);

            // Call the onClone callback if provided
            if (onClone) {
                onClone();
            }
        };
        const handleDeleteSection = (containerId: string, rteId:string) => {
            // Check if this is an AI-created announcement
            const isAIAnnouncement = createWithAI && (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement");

            if (isAIAnnouncement) {
                // For AI announcements, we need to remove from announcementGuideMetaData
                // This would require a new function in the store, for now just call the existing one
                clearRteDetails(containerId, rteId);
            } else {
                // For banners and non-AI content, use the regular clear function
                clearRteDetails(containerId, rteId);
            }

            // Call the handleDeleteRTESection callback to update section counts
            handleDeleteRTESection(index);
        };
        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {
            event.preventDefault();

            const clipboardData = event.clipboardData;
            const pastedText = clipboardData.getData("text/plain");
            const pastedHtml = clipboardData.getData("text/html");

            if (pastedHtml) {
                const isRTEContent = pastedHtml.includes("<!--RTE-->");
                if (isRTEContent) {
                    insertContent(pastedHtml);
                } else {
                    insertContent(pastedHtml);
                }
            } else {
                insertContent(pastedText);
            }
        };


        const insertContent = (content: string) => {
            if (editingRTEId) {
                const editorRef = getEditorRef(editingRTEId);
                if (editorRef?.current) {
                    const editor = (editorRef.current as any).editor;
                    editor.selection.insertHTML(content);
                }
            }
        };
        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);
        useEffect(() => {
    const dir = document.body.getAttribute("dir") || "ltr";
    setIsRtlDirection(dir.toLowerCase() === "rtl");
}, []);

        // Cleanup effect to prevent memory leaks
        useEffect(() => {
            return () => {
                // Clear editor refs on unmount
                editorRefs.current.clear();
                containerRefs.current.clear();
            };
        }, []);

    // Function to toggle toolbar visibility for a specific RTE
    const toggleToolbar = (rteId: string) => {
        setToolbarVisible(prev => {
            const newState = {
                ...prev,
                [rteId]: !prev[rteId] // Toggle between true/false, defaults to false (hidden)
            };
            console.log(`Toggling toolbar for ${rteId}:`, newState[rteId]);
            return newState;
        });
    };

    // Memoized config to prevent unnecessary recreations
    const joditConfig = useMemo(() => {
        const config: any = {
            readonly: false,
            direction: isRtlDirection ? 'rtl' : 'ltr',
            language: 'en',
            toolbarSticky: false,
            toolbarAdaptive: false,
            toolbar: true,
            shadowRoot: null,
            buttons: [
                'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',
                'font', 'fontsize', 'link',
                {
                    name: 'more',
                    iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',
                    list: [
                        'source', 'image', 'video', 'table',
                        'align', 'undo', 'redo', '|',
                        'hr', 'eraser', 'copyformat',
                        'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',
                        'outdent', 'indent', 'paragraph',
                    ]
                }
            ],
            autofocus: true,
            cursorAfterAutofocus: 'end',
            enter: 'p', // Use P tags for paragraphs
            enterBlock: 'p', // Use p for block elements
            iframe: false,
            ownerDocument: document,
            ownerWindow: window,
            disablePlugins: ['paste'], // Disable paste plugin to avoid conflicts
            events: {
                onPaste: handlePaste,
                afterInit: (editor: any) => {
                    try {
                        if (editor && editor.editor) {
                            console.log('Jodit editor initialized successfully');

                            // Enhanced Enter key handling
                            editor.e.on('keydown', (e: KeyboardEvent) => {
                                if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey) {
                                    e.preventDefault();

                                    // Get current selection
                                    const selection = editor.s;
                                    const range = selection.range;

                                    if (range) {
                                        // Create new paragraph
                                        const newP = editor.createInside.element('p');
                                        newP.innerHTML = '<br>'; // Add BR to make paragraph visible

                                        // Insert the paragraph
                                        range.insertNode(newP);

                                        // Position cursor at the start of new paragraph
                                        const newRange = editor.editorDocument.createRange();
                                        newRange.setStart(newP, 0);
                                        newRange.collapse(true);

                                        // Apply the selection
                                        selection.selectRange(newRange);

                                        // Ensure the new paragraph is visible
                                        newP.scrollIntoView({ behavior: 'auto', block: 'nearest' });

                                        // Trigger change
                                        editor.synchronizeValues();
                                        editor.events.fire('change');
                                    }

                                    return false;
                                }
                            });
                        }
                    } catch (error) {
                        console.warn('Jodit editor initialization warning:', error);
                    }
                }
            },
            controls: {
                font: {
                    list: {
                        "Poppins, sans-serif": "Poppins",
                        "Roboto, sans-serif": "Roboto",
                        "Comic Sans MS, sans-serif": "Comic Sans MS",
                        "Open Sans, sans-serif": "Open Sans",
                        "Calibri, sans-serif": "Calibri",
                        "Century Gothic, sans-serif": "Century Gothic",
                    }
                }
            }
        };
        return config;
    }, [isRtlDirection, handlePaste]);

        // Determine which containers to use based on guide type
        const isAIAnnouncement = createWithAI && (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement");
        const isAITour = createWithAI && selectedTemplate === "Tour";
        const isTourAnnouncement = isAITour && selectedTemplateTour === "Announcement";
        const isTourBanner = isAITour && selectedTemplateTour === "Banner";
        const isTourTooltip = isAITour && (selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Hotspot");
        const currentStepIndex = currentStep - 1;

        let containersToRender: any[] = [];

        if (isAIAnnouncement && !isTourAnnouncement) {
            // For pure AI announcements (not in tours), use announcementGuideMetaData
            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);
        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {
            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData
            if (toolTipGuideMetaData[currentStepIndex]?.containers) {
                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === "rte");
                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {
                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,
                    rteContainers: containersToRender.length,
                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))
                });
            } else {
                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);
                containersToRender = [];
            }
        } else {
            // For non-AI content, use rtesContainer
            containersToRender = rtesContainer;
        }

        return (
            <>
                {containersToRender.map((item: any) => {
                    let rteText = "";
                    let rteId = "";
                    let id = "";

                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {
                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container
                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers
                        rteText = item.rteBoxValue || "";
                        rteId = item.id;
                        id = item.id;
                    } else {
                        // For non-AI content, get data from rtesContainer
                        rteText = item.rtes?.[0]?.text || "";
                        rteId = item.rtes?.[0]?.id;
                        id = item.id;
                    }

                    if (!id) return null;

                    const isCurrentlyEditing = editingRTEId === id;
                    const currentContainerRef = getContainerRef(id);
                    const currentEditorRef = getEditorRef(id);

                    return (
                        <Box
                            key={id}
                            ref={currentContainerRef}
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                position: "relative",
                                "& .jodit-status-bar-link": {
                                    display: "none !important",
                                },
                                "& .jodit-editor": {
                                    fontFamily: "'Roboto', sans-serif !important",
                                },
                                ".jodit-editor span": {
                                    fontFamily: "'Roboto', sans-serif !important",
                                },
                                ".jodit-toolbar-button button": {
                                    minWidth: "29px !important",
                                },
                                ".jodit-react-container": {
                                    width: selectedTemplate === "Banner" ? "100%" : "100%",
                                    whiteSpace: "pre-wrap",
                                    wordBreak: "break-word",
                                },
                                ".jodit-workplace": {
                                    minHeight: selectedTemplate==="Banner" || (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? "50px !important": null,
                                    maxHeight: (
  selectedTemplate === "Banner" ||
  (selectedTemplate === "Tour" && selectedTemplateTour === "Banner")
)
  ? "50px !important"
  : (
      selectedTemplate === "Announcement" ||
      (selectedTemplate === "Tour" && selectedTemplateTour === "Announcement")
    )
    ? "calc(100vh - 400px) !important"
    : null,
                                    overflow: selectedTemplate==="Banner" || (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ?"hidden" : "auto !important",
                                },
                                ".jodit-container": {
                                    minWidth:selectedTemplate==="Banner" || (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? "50px !important": null,
                                    minHeight: selectedTemplate==="Banner"|| (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? "50px !important": null
                                },
                                ".jodit-toolbar__box": {
                                    display: toolbarVisible[id] === true ? "flex !important" : "none !important",
                                    justifyContent: "center !important",
                                    height: selectedTemplate==="Banner" || (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? "32px !important": null,
                                    maxHeight: selectedTemplate==="Banner"|| (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? "32px !important": null
                                },
                                "& .jodit-wysiwyg": {
                                    lineHeight: "1.5 !important",
                                    minHeight: "50px !important",
                                    padding: "8px !important",
                                },
                                "& .jodit-wysiwyg p": {
                                    margin: "0 0 1em 0 !important",
                                    minHeight: "1.2em !important",
                                    lineHeight: "1.5 !important",
                                },
                                "& .jodit-wysiwyg p:last-child": {
                                    marginBottom: "0 !important",
                                },
                                "& .jodit-wysiwyg p:empty": {
                                    minHeight: "1.2em !important",
                                },
                                "& .jodit-wysiwyg p:empty:before": {
                                    content: "'\\00a0' !important", // Non-breaking space for empty paragraphs
                                    color: "transparent !important",
                                }
                            }}
                            className="qadpt-rte"
                        >
                            {/* Always show Jodit editor with toolbar toggle */}
                            <div style={{ width: "100%", maxWidth: "100%", margin: "0 auto", position: "relative" }}>
                                {/* Toolbar toggle icon */}
                                <IconButton
                                    onClick={() => toggleToolbar(id)}
                                    sx={{
                                        position: "absolute",
                                        bottom: "8px",
                                        right: "8px",
                                        zIndex: 1000,
                                        backgroundColor: "rgba(255, 255, 255, 0.9)",
                                        border: "1px solid #ddd",
                                        borderRadius: "4px",
                                        width: "32px",
                                        height: "32px",
                                        "&:hover": {
                                            backgroundColor: "rgba(255, 255, 255, 1)",
                                        },
                                        svg: {
                                            width: "16px",
                                            height: "16px",
                                            path: {
                                                fill: toolbarVisible[id] === true ? "var(--primarycolor)" : "#666"
                                            }
                                        }
                                    }}
                                    title={toolbarVisible[id] === true ? "Hide Toolbar" : "Show Toolbar"}
                                >
                                    <span dangerouslySetInnerHTML={{ __html: settingsicon }} />
                                </IconButton>

                                <JoditEditor
                                    key={`jodit-${id}`}
                                    ref={currentEditorRef}
                                    value={rteText}
                                    config={joditConfig}
                                    onChange={(newContent) => handleUpdate(newContent, rteId, id)}
                                />

                                {/* Clone and Delete buttons for Announcement/Tooltip/Hotspot */}
                                {/* Clone and Delete buttons overlay for Announcement/Tooltip/Hotspot */}
                                {((selectedTemplate === "Announcement" || selectedTemplate === "Tooltip" || selectedTemplate === "Hotspot") || (selectedTemplateTour === "Announcement" || selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Hotspot")) && (
                                    <div style={{
                                        position: "absolute",
                                        top: "8px",
                                        left: "8px",
                                        zIndex: 1001,
                                        display: "flex",
                                        gap: "4px"
                                    }}>
                                        <IconButton
                                            size="small"
                                            onClick={() => handleCloneContainer(item.id)}
                                            disabled={isCloneDisabled}
                                            title={isCloneDisabled ? translate("Maximum limit of 3 Rich Text sections reached") : translate("Clone Section")}
                                            sx={{
                                                backgroundColor: "rgba(255, 255, 255, 0.9)",
                                                border: "1px solid #ddd",
                                                borderRadius: "4px",
                                                width: "28px",
                                                height: "28px",
                                                "&:hover": {
                                                    backgroundColor: "rgba(255, 255, 255, 1)",
                                                },
                                                svg: {
                                                    height: "16px",
                                                    path: {
                                                        fill:"var(--primarycolor)"
                                                    }
                                                },
                                            }}
                                        >
                                            <span
                                                dangerouslySetInnerHTML={{ __html: copyicon }}
                                                style={{
                                                    opacity: isCloneDisabled ? 0.5 : 1,
                                                    height: '16px'
                                                }}
                                            />
                                        </IconButton>
                                        <IconButton
                                            size="small"
                                            onClick={() => handleDeleteSection(item.id, rteId)}
                                            sx={{
                                                backgroundColor: "rgba(255, 255, 255, 0.9)",
                                                border: "1px solid #ddd",
                                                borderRadius: "4px",
                                                width: "28px",
                                                height: "28px",
                                                "&:hover": {
                                                    backgroundColor: "rgba(255, 255, 255, 1)",
                                                },
                                                svg: {
                                                    height: "16px",
                                                    path: {
                                                        fill:"var(--primarycolor)"
                                                    }
                                                },
                                            }}
                                        >
                                            <span dangerouslySetInnerHTML={{ __html: deleteicon }}
                                                style={{
                                                    height: '16px'
                                                }}
                                            />
                                        </IconButton>
                                    </div>
                                )}
                            </div>
                        </Box>
                    );
                })}
            </>
        );
    }
);

export default RTEsection;
