{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef } from \"react\";\nimport { Box, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, deleteicon, settingsicon } from \"../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer\n  } = useDrawerStore();\n\n  // Individual state management for each RTE\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const [toolbarVisible, setToolbarVisible] = useState({});\n  const contentRef = useRef(\"\");\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      const newRef = /*#__PURE__*/React.createRef();\n      editorRefs.current.set(rteId, newRef);\n      console.log(`Created new editor ref for RTE: ${rteId}`);\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Handle clicks outside the editor - now works with individual RTEs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!editingRTEId) return; // No RTE is currently being edited\n\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Get the container ref for the currently editing RTE\n      const currentContainerRef = getContainerRef(editingRTEId);\n\n      // Check if the target is inside the currently editing RTE or related elements\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) &&\n      // Click outside the current editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null); // Close the currently editing RTE\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [editingRTEId]);\n  useEffect(() => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        setTimeout(() => {\n          //(editorRef.current as any).editor.focus();\n        }, 50);\n      }\n    }\n  }, [editingRTEId]);\n  const handleUpdate = (newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n\n    // Check if this is an AI-created guide\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    console.log(\"RTEsection handleUpdate:\", {\n      createWithAI,\n      selectedTemplate,\n      selectedTemplateTour,\n      isAIAnnouncement,\n      isAITour,\n      isTourBanner,\n      containerId,\n      newContent: newContent.substring(0, 50) + \"...\"\n    });\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        // For Tour+Announcement, use toolTipGuideMetaData\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          // Use the tooltip-specific handler for tour announcements\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        // For pure Announcements, use announcementGuideMetaData\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          // Use the announcement-specific handler\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        // Use the tooltip-specific handler for all tour step types\n        handleTooltipRTEValue(containerId, newContent);\n        console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\n      } else {\n        var _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n        console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\n          currentStepIndex,\n          containerId,\n          availableContainers: (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.map(c => ({\n            id: c.id,\n            type: c.type\n          }))\n        });\n      }\n    } else {\n      // For non-AI content or other cases, use the regular RTE container system\n      updateRTEContainer(containerId, rteId, newContent);\n      console.log(\"Used updateRTEContainer for non-AI content\");\n    }\n    setIsUnSavedChanges(true);\n  };\n  const handleCloneContainer = containerId => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneRTEContainer(containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    // Check if this is an AI-created announcement\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      // For AI announcements, we need to remove from announcementGuideMetaData\n      // This would require a new function in the store, for now just call the existing one\n      clearRteDetails(containerId, rteId);\n    } else {\n      // For banners and non-AI content, use the regular clear function\n      clearRteDetails(containerId, rteId);\n    }\n\n    // Call the handleDeleteRTESection callback to update section counts\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n\n  // Cleanup effect to prevent memory leaks\n  useEffect(() => {\n    return () => {\n      // Clear editor refs on unmount\n      editorRefs.current.clear();\n      containerRefs.current.clear();\n    };\n  }, []);\n\n  // Function to toggle toolbar visibility for a specific RTE\n  const toggleToolbar = rteId => {\n    setToolbarVisible(prev => {\n      const newState = {\n        ...prev,\n        [rteId]: !prev[rteId] // Toggle between true/false, defaults to false (hidden)\n      };\n      console.log(`Toggling toolbar for ${rteId}:`, newState[rteId]);\n      return newState;\n    });\n  };\n\n  // Function to create config for each RTE with toolbar visibility\n  const createConfig = rteId => ({\n    readonly: false,\n    // all options from https://xdsoft.net/jodit/docs/,\n    direction: isRtlDirection ? 'rtl' : 'ltr',\n    // Jodit uses 'direction' not just 'rtl'\n    language: 'en',\n    // Optional: change language as well\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    toolbar: toolbarVisible[rteId] === true,\n    // Hide toolbar by default, show when explicitly set to true\n    shadowRoot: null,\n    // Disable shadow DOM to prevent shadowRoot errors\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['source', 'image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    autofocus: true,\n    cursorAfterAutofocus: 'end',\n    enter: 'p',\n    // Use <p> tags for new lines instead of <div> or <br>\n    enterBlock: 'p',\n    // Ensure consistent block element creation\n    createAttributes: {\n      p: {\n        style: 'margin: 0 0 1em 0;'\n      }\n    },\n    defaultActionOnPaste: 'insert_clear_html',\n    // Clean paste behavior\n    useEnterForParagraph: true,\n    // Enable proper paragraph creation on Enter\n    useSplitMode: false,\n    // Disable split mode to ensure proper line behavior\n    processPasteHTML: true,\n    // Process HTML on paste\n    askBeforePasteHTML: false,\n    // Don't ask before pasting HTML\n    askBeforePasteFromWord: false,\n    // Don't ask before pasting from Word\n    defaultLineHeight: 1.5,\n    // Set default line height\n    iframe: false,\n    // Disable iframe mode to prevent DOM issues\n    ownerDocument: document,\n    // Explicitly set owner document\n    ownerWindow: window,\n    // Explicitly set owner window\n    cleanHTML: {\n      allowTags: 'p,br,strong,em,u,s,a,ul,ol,li,h1,h2,h3,h4,h5,h6,span,div',\n      allowAttributes: 'href,target,style,class'\n    },\n    events: {\n      onPaste: handlePaste,\n      // Attach custom onPaste handler\n      afterInit: editor => {\n        try {\n          // Safely initialize editor without DOM manipulation\n          if (editor && editor.editor) {\n            // Basic initialization without shadowRoot access\n            console.log('Jodit editor initialized successfully');\n          }\n        } catch (error) {\n          console.warn('Jodit editor initialization warning:', error);\n        }\n      }\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  });\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    // For pure AI announcements (not in tours), use announcementGuideMetaData\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData7;\n    // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\n    if ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n      console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\n        totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\n        rteContainers: containersToRender.length,\n        rteData: containersToRender.map(c => ({\n          id: c.id,\n          rteBoxValue: c.rteBoxValue\n        }))\n      });\n    } else {\n      console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\n      containersToRender = [];\n    }\n  } else {\n    // For non-AI content, use rtesContainer\n    containersToRender = rtesContainer;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\n        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        // For non-AI content, get data from rtesContainer\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const isCurrentlyEditing = editingRTEId === id;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          \".jodit-workplace\": {\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : selectedTemplate === \"Announcement\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\" ? \"calc(100vh - 400px) !important\" : null,\n            overflow: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"hidden\" : \"auto !important\"\n          },\n          \".jodit-container\": {\n            minWidth: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null\n          },\n          \".jodit-toolbar__box\": {\n            display: toolbarVisible[id] === true ? \"flex !important\" : \"none !important\",\n            justifyContent: \"center !important\",\n            height: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null\n          },\n          \"& .jodit-wysiwyg p\": {\n            margin: \"0 0 1em 0 !important\",\n            minHeight: \"1em !important\"\n          },\n          \"& .jodit-wysiwyg\": {\n            lineHeight: \"1.5 !important\"\n          }\n        },\n        className: \"qadpt-rte\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            maxWidth: \"100%\",\n            margin: \"0 auto\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => toggleToolbar(id),\n            sx: {\n              position: \"absolute\",\n              bottom: \"8px\",\n              right: \"8px\",\n              zIndex: 1000,\n              backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              width: \"32px\",\n              height: \"32px\",\n              \"&:hover\": {\n                backgroundColor: \"rgba(255, 255, 255, 1)\"\n              },\n              svg: {\n                width: \"16px\",\n                height: \"16px\",\n                path: {\n                  fill: toolbarVisible[id] === true ? \"var(--primarycolor)\" : \"#666\"\n                }\n              }\n            },\n            title: toolbarVisible[id] === true ? \"Hide Toolbar\" : \"Show Toolbar\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: settingsicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: createConfig(id),\n            onChange: newContent => handleUpdate(newContent, rteId, id)\n          }, `jodit-${id}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 33\n          }, this), (selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: \"absolute\",\n              top: \"8px\",\n              left: \"8px\",\n              zIndex: 1001,\n              display: \"flex\",\n              gap: \"4px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleCloneContainer(item.id),\n              disabled: isCloneDisabled,\n              title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n              sx: {\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                border: \"1px solid #ddd\",\n                borderRadius: \"4px\",\n                width: \"28px\",\n                height: \"28px\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                svg: {\n                  height: \"16px\",\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1,\n                  height: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleDeleteSection(item.id, rteId),\n              sx: {\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                border: \"1px solid #ddd\",\n                borderRadius: \"4px\",\n                width: \"28px\",\n                height: \"28px\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                svg: {\n                  height: \"16px\",\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  height: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 29\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n}, \"i7JEa+zK7K8Lge22S5XTx5zBvXk=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"i7JEa+zK7K8Lge22S5XTx5zBvXk=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "Box", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "settingsicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "toolbarVisible", "setToolbarVisible", "contentRef", "editor<PERSON><PERSON><PERSON>", "Map", "containerRefs", "getEditorRef", "rteId", "current", "has", "newRef", "createRef", "set", "console", "log", "get", "getContainerRef", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "editor<PERSON><PERSON>", "setTimeout", "handleUpdate", "newContent", "containerId", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "substring", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "warn", "availableContainers", "map", "c", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "includes", "insertContent", "content", "editor", "selection", "insertHTML", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "clear", "toggleToolbar", "prev", "newState", "createConfig", "readonly", "direction", "language", "toolbarSticky", "toolbarAdaptive", "toolbar", "shadowRoot", "buttons", "name", "iconURL", "list", "autofocus", "cursorAfterAutofocus", "enter", "enterBlock", "createAttributes", "p", "style", "defaultActionOnPaste", "useEnterForParagraph", "useSplitMode", "processPasteHTML", "askBeforePasteHTML", "askBeforePasteFromWord", "defaultLineHeight", "iframe", "ownerDocument", "ownerWindow", "window", "cleanHTML", "allowTags", "allowAttributes", "events", "onPaste", "afterInit", "error", "controls", "font", "containersToRender", "_toolTipGuideMetaData7", "filter", "totalContainers", "length", "rteContainers", "rteData", "rteBoxValue", "children", "item", "rteText", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "isCurrentlyEditing", "currentEditorRef", "sx", "display", "alignItems", "position", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "minHeight", "maxHeight", "overflow", "justifyContent", "height", "margin", "lineHeight", "className", "max<PERSON><PERSON><PERSON>", "onClick", "bottom", "right", "zIndex", "backgroundColor", "border", "borderRadius", "svg", "path", "fill", "title", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "config", "onChange", "top", "left", "gap", "size", "disabled", "opacity", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef } from \"react\";\r\nimport { Box, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, deleteicon, settingsicon } from \"../../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const [toolbarVisible, setToolbarVisible] = useState<{[key: string]: boolean}>({});\r\n        const contentRef = useRef<string>(\"\");\r\n\r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                const newRef = React.createRef();\r\n                editorRefs.current.set(rteId, newRef);\r\n                console.log(`Created new editor ref for RTE: ${rteId}`);\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Handle clicks outside the editor - now works with individual RTEs\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!editingRTEId) return; // No RTE is currently being edited\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                // Get the container ref for the currently editing RTE\r\n                const currentContainerRef = getContainerRef(editingRTEId);\r\n\r\n                // Check if the target is inside the currently editing RTE or related elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container\r\n                    !isInsidePopup && // Click outside the popup\r\n                    !isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n                    !isInsideWorkplacePopup && // Click outside the workplace popup\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isLinkPopup && // Click outside link input popup\r\n                    !isInsideToolbarButton && // Click outside the toolbar button\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [editingRTEId]);\r\n\r\n        useEffect(() => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    setTimeout(() => {\r\n                        //(editorRef.current as any).editor.focus();\r\n                    }, 50);\r\n                }\r\n            }\r\n        }, [editingRTEId]);\r\n\r\n\r\n\r\n        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            // Check if this is an AI-created guide\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            console.log(\"RTEsection handleUpdate:\", {\r\n                createWithAI,\r\n                selectedTemplate,\r\n                selectedTemplateTour,\r\n                isAIAnnouncement,\r\n                isAITour,\r\n                isTourBanner,\r\n                containerId,\r\n                newContent: newContent.substring(0, 50) + \"...\"\r\n            });\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    // For Tour+Announcement, use toolTipGuideMetaData\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        // Use the tooltip-specific handler for tour announcements\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    // For pure Announcements, use announcementGuideMetaData\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        // Use the announcement-specific handler\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    // Use the tooltip-specific handler for all tour step types\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\r\n                } else {\r\n                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\r\n                        currentStepIndex,\r\n                        containerId,\r\n                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))\r\n                    });\r\n                }\r\n            } else {\r\n                // For non-AI content or other cases, use the regular RTE container system\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n                console.log(\"Used updateRTEContainer for non-AI content\");\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n        };\r\n        const handleCloneContainer = (containerId: string) => {\r\n            // Check if cloning is disabled due to section limits\r\n            if (isCloneDisabled) {\r\n                return; // Don't clone if limit is reached\r\n            }\r\n\r\n            // Call the clone function from the store\r\n            cloneRTEContainer(containerId);\r\n\r\n            // Call the onClone callback if provided\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n        const handleDeleteSection = (containerId: string, rteId:string) => {\r\n            // Check if this is an AI-created announcement\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                // For AI announcements, we need to remove from announcementGuideMetaData\r\n                // This would require a new function in the store, for now just call the existing one\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                // For banners and non-AI content, use the regular clear function\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            // Call the handleDeleteRTESection callback to update section counts\r\n            handleDeleteRTESection(index);\r\n        };\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n\r\n        const insertContent = (content: string) => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        useEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n\r\n        // Cleanup effect to prevent memory leaks\r\n        useEffect(() => {\r\n            return () => {\r\n                // Clear editor refs on unmount\r\n                editorRefs.current.clear();\r\n                containerRefs.current.clear();\r\n            };\r\n        }, []);\r\n\r\n    // Function to toggle toolbar visibility for a specific RTE\r\n    const toggleToolbar = (rteId: string) => {\r\n        setToolbarVisible(prev => {\r\n            const newState = {\r\n                ...prev,\r\n                [rteId]: !prev[rteId] // Toggle between true/false, defaults to false (hidden)\r\n            };\r\n            console.log(`Toggling toolbar for ${rteId}:`, newState[rteId]);\r\n            return newState;\r\n        });\r\n    };\r\n\r\n    // Function to create config for each RTE with toolbar visibility\r\n    const createConfig = (rteId: string) => ({\r\n        readonly: false, // all options from https://xdsoft.net/jodit/docs/,\r\n        direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n\r\n// Jodit uses 'direction' not just 'rtl'\r\n        language:  'en', // Optional: change language as well\r\n        toolbarSticky: false,\r\n        toolbarAdaptive: false,\r\n        toolbar: toolbarVisible[rteId] === true, // Hide toolbar by default, show when explicitly set to true\r\n        shadowRoot: null, // Disable shadow DOM to prevent shadowRoot errors\r\n        buttons: [\r\n\r\n        'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n        'font', 'fontsize', 'link',\r\n        {\r\n            name: 'more',\r\n            iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n            list: [\r\n                        'source',\r\n                        'image', 'video', 'table',\r\n                'align', 'undo', 'redo', '|',\r\n                'hr', 'eraser', 'copyformat',\r\n                'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n                'outdent', 'indent', 'paragraph',\r\n            ]\r\n        }\r\n    ],\r\n    autofocus: true,\r\n    cursorAfterAutofocus: 'end' as const,\r\n    enter: 'p' as const, // Use <p> tags for new lines instead of <div> or <br>\r\n    enterBlock: 'p' as const, // Ensure consistent block element creation\r\n    createAttributes: {\r\n        p: { style: 'margin: 0 0 1em 0;' }\r\n    },\r\n    defaultActionOnPaste: 'insert_clear_html' as const, // Clean paste behavior\r\n    useEnterForParagraph: true, // Enable proper paragraph creation on Enter\r\n    useSplitMode: false, // Disable split mode to ensure proper line behavior\r\n    processPasteHTML: true, // Process HTML on paste\r\n    askBeforePasteHTML: false, // Don't ask before pasting HTML\r\n    askBeforePasteFromWord: false, // Don't ask before pasting from Word\r\n    defaultLineHeight: 1.5, // Set default line height\r\n    iframe: false, // Disable iframe mode to prevent DOM issues\r\n    ownerDocument: document, // Explicitly set owner document\r\n    ownerWindow: window, // Explicitly set owner window\r\n    cleanHTML: {\r\n        allowTags: 'p,br,strong,em,u,s,a,ul,ol,li,h1,h2,h3,h4,h5,h6,span,div',\r\n        allowAttributes: 'href,target,style,class'\r\n    },\r\n    events: {\r\n        onPaste: handlePaste, // Attach custom onPaste handler\r\n        afterInit: (editor: any) => {\r\n            try {\r\n                // Safely initialize editor without DOM manipulation\r\n                if (editor && editor.editor) {\r\n                    // Basic initialization without shadowRoot access\r\n                    console.log('Jodit editor initialized successfully');\r\n                }\r\n            } catch (error) {\r\n                console.warn('Jodit editor initialization warning:', error);\r\n            }\r\n        }\r\n    },\r\n    controls: {\r\n        font: {\r\n            list: {\r\n                \"Poppins, sans-serif\": \"Poppins\",\r\n                \"Roboto, sans-serif\": \"Roboto\",\r\n                \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                \"Open Sans, sans-serif\": \"Open Sans\",\r\n                \"Calibri, sans-serif\": \"Calibri\",\r\n                \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n            }\r\n        }\r\n            }\r\n    });\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            // For pure AI announcements (not in tours), use announcementGuideMetaData\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\r\n                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\r\n                    rteContainers: containersToRender.length,\r\n                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))\r\n                });\r\n            } else {\r\n                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            // For non-AI content, use rtesContainer\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\r\n                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        // For non-AI content, get data from rtesContainer\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n                    const isCurrentlyEditing = editingRTEId === id;\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                \".jodit-workplace\": {\r\n                                    minHeight: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    maxHeight: (\r\n  selectedTemplate === \"Banner\" ||\r\n  (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\")\r\n)\r\n  ? \"50px !important\"\r\n  : (\r\n      selectedTemplate === \"Announcement\" ||\r\n      (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\")\r\n    )\r\n    ? \"calc(100vh - 400px) !important\"\r\n    : null,\r\n                                    overflow: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ?\"hidden\" : \"auto !important\",\r\n                                },\r\n                                \".jodit-container\": {\r\n                                    minWidth:selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    minHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null\r\n                                },\r\n                                \".jodit-toolbar__box\": {\r\n                                    display: toolbarVisible[id] === true ? \"flex !important\" : \"none !important\",\r\n                                    justifyContent: \"center !important\",\r\n                                    height: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null,\r\n                                    maxHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null\r\n                                },\r\n                                \"& .jodit-wysiwyg p\": {\r\n                                    margin: \"0 0 1em 0 !important\",\r\n                                    minHeight: \"1em !important\",\r\n                                },\r\n                                \"& .jodit-wysiwyg\": {\r\n                                    lineHeight: \"1.5 !important\",\r\n                                }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {/* Always show Jodit editor with toolbar toggle */}\r\n                            <div style={{ width: \"100%\", maxWidth: \"100%\", margin: \"0 auto\", position: \"relative\" }}>\r\n                                {/* Toolbar toggle icon */}\r\n                                <IconButton\r\n                                    onClick={() => toggleToolbar(id)}\r\n                                    sx={{\r\n                                        position: \"absolute\",\r\n                                        bottom: \"8px\",\r\n                                        right: \"8px\",\r\n                                        zIndex: 1000,\r\n                                        backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                        border: \"1px solid #ddd\",\r\n                                        borderRadius: \"4px\",\r\n                                        width: \"32px\",\r\n                                        height: \"32px\",\r\n                                        \"&:hover\": {\r\n                                            backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                        },\r\n                                        svg: {\r\n                                            width: \"16px\",\r\n                                            height: \"16px\",\r\n                                            path: {\r\n                                                fill: toolbarVisible[id] === true ? \"var(--primarycolor)\" : \"#666\"\r\n                                            }\r\n                                        }\r\n                                    }}\r\n                                    title={toolbarVisible[id] === true ? \"Hide Toolbar\" : \"Show Toolbar\"}\r\n                                >\r\n                                    <span dangerouslySetInnerHTML={{ __html: settingsicon }} />\r\n                                </IconButton>\r\n\r\n                                <JoditEditor\r\n                                    key={`jodit-${id}`}\r\n                                    ref={currentEditorRef}\r\n                                    value={rteText}\r\n                                    config={createConfig(id)}\r\n                                    onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                />\r\n\r\n                                {/* Clone and Delete buttons for Announcement/Tooltip/Hotspot */}\r\n                                {/* Clone and Delete buttons overlay for Announcement/Tooltip/Hotspot */}\r\n                                {((selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\") || (selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\")) && (\r\n                                    <div style={{\r\n                                        position: \"absolute\",\r\n                                        top: \"8px\",\r\n                                        left: \"8px\",\r\n                                        zIndex: 1001,\r\n                                        display: \"flex\",\r\n                                        gap: \"4px\"\r\n                                    }}>\r\n                                        <IconButton\r\n                                            size=\"small\"\r\n                                            onClick={() => handleCloneContainer(item.id)}\r\n                                            disabled={isCloneDisabled}\r\n                                            title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                            sx={{\r\n                                                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                                border: \"1px solid #ddd\",\r\n                                                borderRadius: \"4px\",\r\n                                                width: \"28px\",\r\n                                                height: \"28px\",\r\n                                                \"&:hover\": {\r\n                                                    backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                                },\r\n                                                svg: {\r\n                                                    height: \"16px\",\r\n                                                    path: {\r\n                                                        fill:\"var(--primarycolor)\"\r\n                                                    }\r\n                                                },\r\n                                            }}\r\n                                        >\r\n                                            <span\r\n                                                dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                                style={{\r\n                                                    opacity: isCloneDisabled ? 0.5 : 1,\r\n                                                    height: '16px'\r\n                                                }}\r\n                                            />\r\n                                        </IconButton>\r\n                                        <IconButton\r\n                                            size=\"small\"\r\n                                            onClick={() => handleDeleteSection(item.id, rteId)}\r\n                                            sx={{\r\n                                                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                                border: \"1px solid #ddd\",\r\n                                                borderRadius: \"4px\",\r\n                                                width: \"28px\",\r\n                                                height: \"28px\",\r\n                                                \"&:hover\": {\r\n                                                    backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                                },\r\n                                                svg: {\r\n                                                    height: \"16px\",\r\n                                                    path: {\r\n                                                        fill:\"var(--primarycolor)\"\r\n                                                    }\r\n                                                },\r\n                                            }}\r\n                                        >\r\n                                            <span dangerouslySetInnerHTML={{ __html: deleteicon }}\r\n                                                style={{\r\n                                                    height: '16px'\r\n                                                }}\r\n                                            />\r\n                                        </IconButton>\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACtE,SAASC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAC/C,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,QAAQ,6BAA6B;AAChF,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGd,UAAU,CAAAe,EAAA,GAAAD,EAAA,CACpD,CAAC;EAAEE,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAAT,EAAA;EACvG,MAAM;IAAEU,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM;IACFkB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAC,GAAGnC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAA2B,CAAC,CAAC,CAAC;EAClF,MAAM+C,UAAU,GAAG7C,MAAM,CAAS,EAAE,CAAC;;EAErC;EACA,MAAM8C,UAAU,GAAG9C,MAAM,CAAoC,IAAI+C,GAAG,CAAC,CAAC,CAAC;EACvE,MAAMC,aAAa,GAAGhD,MAAM,CAA+C,IAAI+C,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAME,YAAY,GAAIC,KAAa,IAAK;IACpC,IAAI,CAACJ,UAAU,CAACK,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAChC,MAAMG,MAAM,gBAAGxD,KAAK,CAACyD,SAAS,CAAC,CAAC;MAChCR,UAAU,CAACK,OAAO,CAACI,GAAG,CAACL,KAAK,EAAEG,MAAM,CAAC;MACrCG,OAAO,CAACC,GAAG,CAAC,mCAAmCP,KAAK,EAAE,CAAC;IAC3D;IACA,OAAOJ,UAAU,CAACK,OAAO,CAACO,GAAG,CAACR,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMS,eAAe,GAAIT,KAAa,IAAK;IACvC,IAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACnCF,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,eAAErD,KAAK,CAACyD,SAAS,CAAC,CAAC,CAAC;IACvD;IACA,OAAON,aAAa,CAACG,OAAO,CAACO,GAAG,CAACR,KAAK,CAAC;EAC3C,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACZ,MAAM6D,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC9C,IAAI,CAACxB,YAAY,EAAE,OAAO,CAAC;;MAE3B,MAAMyB,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,cAAAV,qBAAA,uBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMO,kBAAkB,IAAAX,sBAAA,GAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,cAAAT,sBAAA,uBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMQ,sBAAsB,GAAGD,kBAAkB,MAAAV,sBAAA,GAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,cAAAR,sBAAA,uBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMS,iBAAiB,GAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAd,sBAAA,GAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,cAAAP,sBAAA,uBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMa,qBAAqB,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMa,cAAc,GAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,MAAMc,mBAAmB,GAAGvB,eAAe,CAAClB,YAAY,CAAC;;MAEzD;MACA,IACIyC,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE/B,OAAO,IAC5B,CAAC+B,mBAAmB,CAAC/B,OAAO,CAACsB,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAAI;MAC/D,CAACG,aAAa;MAAI;MAClB,CAACI,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACG,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAI;MAC1B,CAACC,cAAc,IACf,CAACf,yBAAyB,IAC1B,CAACG,oBAAoB,EACvB;QACE3B,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3B;IACJ,CAAC;IAED6B,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAACnB,YAAY,CAAC,CAAC;EAElB1C,SAAS,CAAC,MAAM;IACZ,IAAI0C,YAAY,EAAE;MACd,MAAM4C,SAAS,GAAGpC,YAAY,CAACR,YAAY,CAAC;MAC5C,IAAI4C,SAAS,aAATA,SAAS,eAATA,SAAS,CAAElC,OAAO,EAAE;QACpBmC,UAAU,CAAC,MAAM;UACb;QAAA,CACH,EAAE,EAAE,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAAC7C,YAAY,CAAC,CAAC;EAIlB,MAAM8C,YAAY,GAAGA,CAACC,UAAkB,EAAEtC,KAAa,EAAEuC,WAAmB,KAAK;IAC7E5C,UAAU,CAACM,OAAO,GAAGqC,UAAU;;IAE/B;IACA,MAAME,gBAAgB,GAAGpD,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IACzH,MAAM0D,QAAQ,GAAGrD,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAM4D,kBAAkB,GAAGD,QAAQ,IAAI1D,oBAAoB,KAAK,cAAc;IAC9E,MAAM4D,YAAY,GAAGF,QAAQ,IAAI1D,oBAAoB,KAAK,QAAQ;IAClE,MAAM6D,aAAa,GAAGH,QAAQ,KAAK1D,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5GuB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACpCnB,YAAY;MACZN,gBAAgB;MAChBC,oBAAoB;MACpByD,gBAAgB;MAChBC,QAAQ;MACRE,YAAY;MACZJ,WAAW;MACXD,UAAU,EAAEA,UAAU,CAACO,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;IAC9C,CAAC,CAAC;IAEF,IAAIL,gBAAgB,EAAE;MAClB,MAAMM,gBAAgB,GAAGzD,WAAW,GAAG,CAAC;MAExC,IAAIqD,kBAAkB,EAAE;QAAA,IAAAK,qBAAA,EAAAC,sBAAA;QACpB;QACA,MAAMC,gBAAgB,IAAAF,qBAAA,GAAG9D,oBAAoB,CAAC6D,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAACzB,EAAE,KAAKY,WAAW,IAAIa,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UAClB;UACA9D,qBAAqB,CAACoD,WAAW,EAAED,UAAU,CAAC;QAClD;MACJ,CAAC,MAAM;QAAA,IAAAgB,qBAAA,EAAAC,sBAAA;QACH;QACA,MAAMC,qBAAqB,IAAAF,qBAAA,GAAGtE,yBAAyB,CAAC8D,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,IAAKA,SAAS,CAACzB,EAAE,KAAKY,WAAW,IAAIa,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACvB;UACAtE,0BAA0B,CAACqD,WAAW,EAAED,UAAU,CAAC;QACvD;MACJ;IACJ,CAAC,MAAM,IAAIG,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAa,sBAAA,EAAAC,sBAAA;MACpD;MACA,MAAMZ,gBAAgB,GAAGzD,WAAW,GAAG,CAAC;MACxC,MAAM4D,gBAAgB,IAAAQ,sBAAA,GAAGxE,oBAAoB,CAAC6D,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAACzB,EAAE,KAAKY,WAAW,IAAIa,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QAClB;QACA9D,qBAAqB,CAACoD,WAAW,EAAED,UAAU,CAAC;QAC9ChC,OAAO,CAACC,GAAG,CAAC,kCAAkCxB,oBAAoB,kBAAkB,CAAC;MACzF,CAAC,MAAM;QAAA,IAAA4E,sBAAA,EAAAC,sBAAA;QACHtD,OAAO,CAACuD,IAAI,CAAC,kCAAkC9E,oBAAoB,OAAO,EAAE;UACxE+D,gBAAgB;UAChBP,WAAW;UACXuB,mBAAmB,GAAAH,sBAAA,GAAE1E,oBAAoB,CAAC6D,gBAAgB,CAAC,cAAAa,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCT,UAAU,cAAAU,sBAAA,uBAAlDA,sBAAA,CAAoDG,GAAG,CAACC,CAAC,KAAK;YAAErC,EAAE,EAAEqC,CAAC,CAACrC,EAAE;YAAE0B,IAAI,EAAEW,CAAC,CAACX;UAAK,CAAC,CAAC;QAClH,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH;MACA3E,kBAAkB,CAAC6D,WAAW,EAAEvC,KAAK,EAAEsC,UAAU,CAAC;MAClDhC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC7D;IAEA5B,mBAAmB,CAAC,IAAI,CAAC;EAC7B,CAAC;EACD,MAAMsF,oBAAoB,GAAI1B,WAAmB,IAAK;IAClD;IACA,IAAIlE,eAAe,EAAE;MACjB,OAAO,CAAC;IACZ;;IAEA;IACAO,iBAAiB,CAAC2D,WAAW,CAAC;;IAE9B;IACA,IAAInE,OAAO,EAAE;MACTA,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,MAAM8F,mBAAmB,GAAGA,CAAC3B,WAAmB,EAAEvC,KAAY,KAAK;IAC/D;IACA,MAAMwC,gBAAgB,GAAGpD,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAEzH,IAAIyD,gBAAgB,EAAE;MAClB;MACA;MACA3D,eAAe,CAAC0D,WAAW,EAAEvC,KAAK,CAAC;IACvC,CAAC,MAAM;MACH;MACAnB,eAAe,CAAC0D,WAAW,EAAEvC,KAAK,CAAC;IACvC;;IAEA;IACA/B,sBAAsB,CAACC,KAAK,CAAC;EACjC,CAAC;EACD,MAAMiG,WAAW,GAAIxD,KAA2C,IAAK;IACjEA,KAAK,CAACyD,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAG1D,KAAK,CAAC0D,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACZ,MAAMC,YAAY,GAAGD,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAID,YAAY,EAAE;QACdE,aAAa,CAACH,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHG,aAAa,CAACH,UAAU,CAAC;MAC7B;IACJ,CAAC,MAAM;MACHG,aAAa,CAACL,UAAU,CAAC;IAC7B;EACJ,CAAC;EAGD,MAAMK,aAAa,GAAIC,OAAe,IAAK;IACvC,IAAIrF,YAAY,EAAE;MACd,MAAM4C,SAAS,GAAGpC,YAAY,CAACR,YAAY,CAAC;MAC5C,IAAI4C,SAAS,aAATA,SAAS,eAATA,SAAS,CAAElC,OAAO,EAAE;QACpB,MAAM4E,MAAM,GAAI1C,SAAS,CAAClC,OAAO,CAAS4E,MAAM;QAChDA,MAAM,CAACC,SAAS,CAACC,UAAU,CAACH,OAAO,CAAC;MACxC;IACJ;EACJ,CAAC;EACD,MAAM,CAACI,cAAc,EAAEC,iBAAiB,CAAC,GAAGrI,QAAQ,CAAU,KAAK,CAAC;EACpEC,SAAS,CAAC,MAAM;IACpB,MAAMqI,GAAG,GAAG7D,QAAQ,CAAC8D,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEE;EACAxI,SAAS,CAAC,MAAM;IACZ,OAAO,MAAM;MACT;MACA+C,UAAU,CAACK,OAAO,CAACqF,KAAK,CAAC,CAAC;MAC1BxF,aAAa,CAACG,OAAO,CAACqF,KAAK,CAAC,CAAC;IACjC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEV;EACA,MAAMC,aAAa,GAAIvF,KAAa,IAAK;IACrCN,iBAAiB,CAAC8F,IAAI,IAAI;MACtB,MAAMC,QAAQ,GAAG;QACb,GAAGD,IAAI;QACP,CAACxF,KAAK,GAAG,CAACwF,IAAI,CAACxF,KAAK,CAAC,CAAC;MAC1B,CAAC;MACDM,OAAO,CAACC,GAAG,CAAC,wBAAwBP,KAAK,GAAG,EAAEyF,QAAQ,CAACzF,KAAK,CAAC,CAAC;MAC9D,OAAOyF,QAAQ;IACnB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMC,YAAY,GAAI1F,KAAa,KAAM;IACrC2F,QAAQ,EAAE,KAAK;IAAE;IACjBC,SAAS,EAAEZ,cAAc,GAAG,KAAK,GAAY,KAAc;IAEnE;IACQa,QAAQ,EAAG,IAAI;IAAE;IACjBC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAEvG,cAAc,CAACO,KAAK,CAAC,KAAK,IAAI;IAAE;IACzCiG,UAAU,EAAE,IAAI;IAAE;IAClBC,OAAO,EAAE,CAET,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACIC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACM,QAAQ,EACR,OAAO,EAAE,OAAO,EAAE,OAAO,EACjC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAC9D,SAAS,EAAE,QAAQ,EAAE,WAAW;IAExC,CAAC,CACJ;IACDC,SAAS,EAAE,IAAI;IACfC,oBAAoB,EAAE,KAAc;IACpCC,KAAK,EAAE,GAAY;IAAE;IACrBC,UAAU,EAAE,GAAY;IAAE;IAC1BC,gBAAgB,EAAE;MACdC,CAAC,EAAE;QAAEC,KAAK,EAAE;MAAqB;IACrC,CAAC;IACDC,oBAAoB,EAAE,mBAA4B;IAAE;IACpDC,oBAAoB,EAAE,IAAI;IAAE;IAC5BC,YAAY,EAAE,KAAK;IAAE;IACrBC,gBAAgB,EAAE,IAAI;IAAE;IACxBC,kBAAkB,EAAE,KAAK;IAAE;IAC3BC,sBAAsB,EAAE,KAAK;IAAE;IAC/BC,iBAAiB,EAAE,GAAG;IAAE;IACxBC,MAAM,EAAE,KAAK;IAAE;IACfC,aAAa,EAAEhG,QAAQ;IAAE;IACzBiG,WAAW,EAAEC,MAAM;IAAE;IACrBC,SAAS,EAAE;MACPC,SAAS,EAAE,0DAA0D;MACrEC,eAAe,EAAE;IACrB,CAAC;IACDC,MAAM,EAAE;MACJC,OAAO,EAAEzD,WAAW;MAAE;MACtB0D,SAAS,EAAGhD,MAAW,IAAK;QACxB,IAAI;UACA;UACA,IAAIA,MAAM,IAAIA,MAAM,CAACA,MAAM,EAAE;YACzB;YACAvE,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACxD;QACJ,CAAC,CAAC,OAAOuH,KAAK,EAAE;UACZxH,OAAO,CAACuD,IAAI,CAAC,sCAAsC,EAAEiE,KAAK,CAAC;QAC/D;MACJ;IACJ,CAAC;IACDC,QAAQ,EAAE;MACNC,IAAI,EAAE;QACF3B,IAAI,EAAE;UACF,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAClC;MACJ;IACI;EACR,CAAC,CAAC;;EAEE;EACA,MAAM7D,gBAAgB,GAAGpD,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAM0D,QAAQ,GAAGrD,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAM4D,kBAAkB,GAAGD,QAAQ,IAAI1D,oBAAoB,KAAK,cAAc;EAC9E,MAAM4D,YAAY,GAAGF,QAAQ,IAAI1D,oBAAoB,KAAK,QAAQ;EAClE,MAAM6D,aAAa,GAAGH,QAAQ,KAAK1D,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAM+D,gBAAgB,GAAGzD,WAAW,GAAG,CAAC;EAExC,IAAI4I,kBAAyB,GAAG,EAAE;EAElC,IAAIzF,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IACzC;IACAuF,kBAAkB,GAAG3I,8BAA8B,CAACwD,gBAAgB,EAAE,KAAK,CAAC;EAChF,CAAC,MAAM,IAAIL,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAAwF,sBAAA;IAC1E;IACA,KAAAA,sBAAA,GAAIjJ,oBAAoB,CAAC6D,gBAAgB,CAAC,cAAAoF,sBAAA,eAAtCA,sBAAA,CAAwChF,UAAU,EAAE;MACpD+E,kBAAkB,GAAGhJ,oBAAoB,CAAC6D,gBAAgB,CAAC,CAACI,UAAU,CAACiF,MAAM,CAACnE,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAK,KAAK,CAAC;MACpG/C,OAAO,CAACC,GAAG,CAAC,yDAAyDxB,oBAAoB,SAAS+D,gBAAgB,GAAG,EAAE;QACnHsF,eAAe,EAAEnJ,oBAAoB,CAAC6D,gBAAgB,CAAC,CAACI,UAAU,CAACmF,MAAM;QACzEC,aAAa,EAAEL,kBAAkB,CAACI,MAAM;QACxCE,OAAO,EAAEN,kBAAkB,CAAClE,GAAG,CAACC,CAAC,KAAK;UAAErC,EAAE,EAAEqC,CAAC,CAACrC,EAAE;UAAE6G,WAAW,EAAExE,CAAC,CAACwE;QAAY,CAAC,CAAC;MACnF,CAAC,CAAC;IACN,CAAC,MAAM;MACHlI,OAAO,CAACuD,IAAI,CAAC,iDAAiD9E,oBAAoB,SAAS+D,gBAAgB,EAAE,CAAC;MAC9GmF,kBAAkB,GAAG,EAAE;IAC3B;EACJ,CAAC,MAAM;IACH;IACAA,kBAAkB,GAAGxJ,aAAa;EACtC;EAEA,oBACIhB,OAAA,CAAAE,SAAA;IAAA8K,QAAA,EACKR,kBAAkB,CAAClE,GAAG,CAAE2E,IAAS,IAAK;MACnC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAI3I,KAAK,GAAG,EAAE;MACd,IAAI2B,EAAE,GAAG,EAAE;MAEX,IAAKa,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH;QACA;QACAiG,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChCxI,KAAK,GAAG0I,IAAI,CAAC/G,EAAE;QACfA,EAAE,GAAG+G,IAAI,CAAC/G,EAAE;MAChB,CAAC,MAAM;QAAA,IAAAiH,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACH;QACAJ,OAAO,GAAG,EAAAC,UAAA,GAAAF,IAAI,CAACM,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpCjJ,KAAK,IAAA8I,WAAA,GAAGJ,IAAI,CAACM,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBpH,EAAE;QAC1BA,EAAE,GAAG+G,IAAI,CAAC/G,EAAE;MAChB;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAEpB,MAAMuH,kBAAkB,GAAG3J,YAAY,KAAKoC,EAAE;MAC9C,MAAMK,mBAAmB,GAAGvB,eAAe,CAACkB,EAAE,CAAC;MAC/C,MAAMwH,gBAAgB,GAAGpJ,YAAY,CAAC4B,EAAE,CAAC;MAEzC,oBACIlE,OAAA,CAACT,GAAG;QAEAsB,GAAG,EAAE0D,mBAAoB;QACzBoH,EAAE,EAAE;UACAC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB,0BAA0B,EAAE;YACxBF,OAAO,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACfG,UAAU,EAAE;UAChB,CAAC;UACD,oBAAoB,EAAE;YAClBA,UAAU,EAAE;UAChB,CAAC;UACD,8BAA8B,EAAE;YAC5BC,QAAQ,EAAE;UACd,CAAC;UACD,wBAAwB,EAAE;YACtBC,KAAK,EAAE5K,gBAAgB,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;YACtD6K,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,kBAAkB,EAAE;YAChBC,SAAS,EAAE/K,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACtI+K,SAAS,EAC3ChL,gBAAgB,KAAK,QAAQ,IAC5BA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAEhE,iBAAiB,GAEfD,gBAAgB,KAAK,cAAc,IAClCA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,cAAe,GAExE,gCAAgC,GAChC,IAAI;YAC0BgL,QAAQ,EAAEjL,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAE,QAAQ,GAAG;UAC5H,CAAC;UACD,kBAAkB,EAAE;YAChB0K,QAAQ,EAAC3K,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACpI8K,SAAS,EAAE/K,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD,qBAAqB,EAAE;YACnBsK,OAAO,EAAE5J,cAAc,CAACkC,EAAE,CAAC,KAAK,IAAI,GAAG,iBAAiB,GAAG,iBAAiB;YAC5EqI,cAAc,EAAE,mBAAmB;YACnCC,MAAM,EAAEnL,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACnI+K,SAAS,EAAEhL,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD,oBAAoB,EAAE;YAClBmL,MAAM,EAAE,sBAAsB;YAC9BL,SAAS,EAAE;UACf,CAAC;UACD,kBAAkB,EAAE;YAChBM,UAAU,EAAE;UAChB;QACJ,CAAE;QACFC,SAAS,EAAC,WAAW;QAAA3B,QAAA,eAGrBhL,OAAA;UAAKmJ,KAAK,EAAE;YAAE8C,KAAK,EAAE,MAAM;YAAEW,QAAQ,EAAE,MAAM;YAAEH,MAAM,EAAE,QAAQ;YAAEX,QAAQ,EAAE;UAAW,CAAE;UAAAd,QAAA,gBAEpFhL,OAAA,CAACR,UAAU;YACPqN,OAAO,EAAEA,CAAA,KAAM/E,aAAa,CAAC5D,EAAE,CAAE;YACjCyH,EAAE,EAAE;cACAG,QAAQ,EAAE,UAAU;cACpBgB,MAAM,EAAE,KAAK;cACbC,KAAK,EAAE,KAAK;cACZC,MAAM,EAAE,IAAI;cACZC,eAAe,EAAE,0BAA0B;cAC3CC,MAAM,EAAE,gBAAgB;cACxBC,YAAY,EAAE,KAAK;cACnBlB,KAAK,EAAE,MAAM;cACbO,MAAM,EAAE,MAAM;cACd,SAAS,EAAE;gBACPS,eAAe,EAAE;cACrB,CAAC;cACDG,GAAG,EAAE;gBACDnB,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACda,IAAI,EAAE;kBACFC,IAAI,EAAEtL,cAAc,CAACkC,EAAE,CAAC,KAAK,IAAI,GAAG,qBAAqB,GAAG;gBAChE;cACJ;YACJ,CAAE;YACFqJ,KAAK,EAAEvL,cAAc,CAACkC,EAAE,CAAC,KAAK,IAAI,GAAG,cAAc,GAAG,cAAe;YAAA8G,QAAA,eAErEhL,OAAA;cAAMwN,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5N;cAAa;YAAE;cAAA6N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEb7N,OAAA,CAACP,WAAW;YAERoB,GAAG,EAAE6K,gBAAiB;YACtBoC,KAAK,EAAE5C,OAAQ;YACf6C,MAAM,EAAE9F,YAAY,CAAC/D,EAAE,CAAE;YACzB8J,QAAQ,EAAGnJ,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAEtC,KAAK,EAAE2B,EAAE;UAAE,GAJzD,SAASA,EAAE,EAAE;YAAAwJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKrB,CAAC,EAID,CAAExM,gBAAgB,KAAK,cAAc,IAAIA,gBAAgB,KAAK,SAAS,IAAIA,gBAAgB,KAAK,SAAS,IAAMC,oBAAoB,KAAK,cAAc,IAAIA,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAU,kBAChOtB,OAAA;YAAKmJ,KAAK,EAAE;cACR2C,QAAQ,EAAE,UAAU;cACpBmC,GAAG,EAAE,KAAK;cACVC,IAAI,EAAE,KAAK;cACXlB,MAAM,EAAE,IAAI;cACZpB,OAAO,EAAE,MAAM;cACfuC,GAAG,EAAE;YACT,CAAE;YAAAnD,QAAA,gBACEhL,OAAA,CAACR,UAAU;cACP4O,IAAI,EAAC,OAAO;cACZvB,OAAO,EAAEA,CAAA,KAAMrG,oBAAoB,CAACyE,IAAI,CAAC/G,EAAE,CAAE;cAC7CmK,QAAQ,EAAEzN,eAAgB;cAC1B2M,KAAK,EAAE3M,eAAe,GAAGG,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;cACjH4K,EAAE,EAAE;gBACAsB,eAAe,EAAE,0BAA0B;gBAC3CC,MAAM,EAAE,gBAAgB;gBACxBC,YAAY,EAAE,KAAK;gBACnBlB,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE;kBACPS,eAAe,EAAE;gBACrB,CAAC;gBACDG,GAAG,EAAE;kBACDZ,MAAM,EAAE,MAAM;kBACda,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACJ,CAAE;cAAAtC,QAAA,eAEFhL,OAAA;gBACIwN,uBAAuB,EAAE;kBAAEC,MAAM,EAAE9N;gBAAS,CAAE;gBAC9CwJ,KAAK,EAAE;kBACHmF,OAAO,EAAE1N,eAAe,GAAG,GAAG,GAAG,CAAC;kBAClC4L,MAAM,EAAE;gBACZ;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACb7N,OAAA,CAACR,UAAU;cACP4O,IAAI,EAAC,OAAO;cACZvB,OAAO,EAAEA,CAAA,KAAMpG,mBAAmB,CAACwE,IAAI,CAAC/G,EAAE,EAAE3B,KAAK,CAAE;cACnDoJ,EAAE,EAAE;gBACAsB,eAAe,EAAE,0BAA0B;gBAC3CC,MAAM,EAAE,gBAAgB;gBACxBC,YAAY,EAAE,KAAK;gBACnBlB,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE;kBACPS,eAAe,EAAE;gBACrB,CAAC;gBACDG,GAAG,EAAE;kBACDZ,MAAM,EAAE,MAAM;kBACda,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACJ,CAAE;cAAAtC,QAAA,eAEFhL,OAAA;gBAAMwN,uBAAuB,EAAE;kBAAEC,MAAM,EAAE7N;gBAAW,CAAE;gBAClDuJ,KAAK,EAAE;kBACHqD,MAAM,EAAE;gBACZ;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC,GAtKD3J,EAAE;QAAAwJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuKN,CAAC;IAEd,CAAC;EAAC,gBACJ,CAAC;AAEX,CAAC;EAAA,QAnjB4B/N,cAAc,EAgBnCJ,cAAc;AAAA,EAoiB1B,CAAC;EAAA,QApjBgCI,cAAc,EAgBnCJ,cAAc;AAAA,EAoiBzB;AAAC6O,GAAA,GAtjBIpO,UAAqC;AAwjB3C,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAkO,GAAA;AAAAC,YAAA,CAAAnO,EAAA;AAAAmO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}