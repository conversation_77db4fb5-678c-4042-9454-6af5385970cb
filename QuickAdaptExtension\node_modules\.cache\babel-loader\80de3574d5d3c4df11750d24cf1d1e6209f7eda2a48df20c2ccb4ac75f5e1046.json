{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef } from \"react\";\nimport { Box, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, deleteicon, settingsicon } from \"../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer\n  } = useDrawerStore();\n\n  // Individual state management for each RTE\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const [toolbarVisible, setToolbarVisible] = useState({});\n  const contentRef = useRef(\"\");\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      editorRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Handle clicks outside the editor - now works with individual RTEs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!editingRTEId) return; // No RTE is currently being edited\n\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Get the container ref for the currently editing RTE\n      const currentContainerRef = getContainerRef(editingRTEId);\n\n      // Check if the target is inside the currently editing RTE or related elements\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) &&\n      // Click outside the current editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null); // Close the currently editing RTE\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [editingRTEId]);\n  useEffect(() => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        setTimeout(() => {\n          //(editorRef.current as any).editor.focus();\n        }, 50);\n      }\n    }\n  }, [editingRTEId]);\n  const handleUpdate = (newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n\n    // Check if this is an AI-created guide\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    console.log(\"RTEsection handleUpdate:\", {\n      createWithAI,\n      selectedTemplate,\n      selectedTemplateTour,\n      isAIAnnouncement,\n      isAITour,\n      isTourBanner,\n      containerId,\n      newContent: newContent.substring(0, 50) + \"...\"\n    });\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        // For Tour+Announcement, use toolTipGuideMetaData\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          // Use the tooltip-specific handler for tour announcements\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        // For pure Announcements, use announcementGuideMetaData\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          // Use the announcement-specific handler\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        // Use the tooltip-specific handler for all tour step types\n        handleTooltipRTEValue(containerId, newContent);\n        console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\n      } else {\n        var _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n        console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\n          currentStepIndex,\n          containerId,\n          availableContainers: (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.map(c => ({\n            id: c.id,\n            type: c.type\n          }))\n        });\n      }\n    } else {\n      // For non-AI content or other cases, use the regular RTE container system\n      updateRTEContainer(containerId, rteId, newContent);\n      console.log(\"Used updateRTEContainer for non-AI content\");\n    }\n    setIsUnSavedChanges(true);\n  };\n  const handleCloneContainer = containerId => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneRTEContainer(containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    // Check if this is an AI-created announcement\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      // For AI announcements, we need to remove from announcementGuideMetaData\n      // This would require a new function in the store, for now just call the existing one\n      clearRteDetails(containerId, rteId);\n    } else {\n      // For banners and non-AI content, use the regular clear function\n      clearRteDetails(containerId, rteId);\n    }\n\n    // Call the handleDeleteRTESection callback to update section counts\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n\n  // Function to toggle toolbar visibility for a specific RTE\n  const toggleToolbar = rteId => {\n    setToolbarVisible(prev => ({\n      ...prev,\n      [rteId]: !prev[rteId] // Toggle between true/false, defaults to false (hidden)\n    }));\n  };\n\n  // Function to create config for each RTE with toolbar visibility\n  const createConfig = rteId => ({\n    readonly: false,\n    // all options from https://xdsoft.net/jodit/docs/,\n    direction: isRtlDirection ? 'rtl' : 'ltr',\n    // Jodit uses 'direction' not just 'rtl'\n    language: 'en',\n    // Optional: change language as well\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    toolbar: toolbarVisible[rteId] === true,\n    // Hide toolbar by default, show when explicitly set to true\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['source', 'image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    autofocus: true,\n    cursorAfterAutofocus: 'end',\n    enter: 'p',\n    // Use <p> tags for new lines instead of <div> or <br>\n    enterBlock: 'p',\n    // Ensure consistent block element creation\n    defaultActionOnPaste: 'insert_clear_html',\n    // Clean paste behavior\n    useEnterForParagraph: true,\n    // Enable proper paragraph creation on Enter\n    useSplitMode: false,\n    // Disable split mode to ensure proper line behavior\n    processPasteHTML: true,\n    // Process HTML on paste\n    askBeforePasteHTML: false,\n    // Don't ask before pasting HTML\n    askBeforePasteFromWord: false,\n    // Don't ask before pasting from Word\n    defaultLineHeight: 1.5,\n    // Set default line height\n    events: {\n      onPaste: handlePaste,\n      // Attach custom onPaste handler\n      afterInit: editor => {\n        // Fix Enter key behavior for proper cursor movement\n        editor.e.on('keydown', e => {\n          if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey) {\n            e.preventDefault();\n            const selection = editor.s;\n            const currentNode = selection.current();\n\n            // Create a new paragraph element\n            const newParagraph = editor.createInside.element('p');\n            newParagraph.innerHTML = '<br>';\n\n            // If we're at the end of a paragraph or in an empty editor\n            if (currentNode) {\n              const parentP = currentNode.closest('p') || currentNode;\n              if (parentP && parentP.parentNode) {\n                // Insert the new paragraph after the current one\n                parentP.parentNode.insertBefore(newParagraph, parentP.nextSibling);\n              } else {\n                // If no parent paragraph, append to the editor\n                editor.editor.appendChild(newParagraph);\n              }\n            } else {\n              // If no current node, append to the editor\n              editor.editor.appendChild(newParagraph);\n            }\n\n            // Move cursor to the new paragraph\n            selection.setCursorIn(newParagraph);\n\n            // Trigger change event\n            editor.synchronizeValues();\n            editor.events.fire('change');\n            return false; // Prevent default behavior\n          }\n        });\n      }\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  });\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    // For pure AI announcements (not in tours), use announcementGuideMetaData\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData7;\n    // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\n    if ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n      console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\n        totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\n        rteContainers: containersToRender.length,\n        rteData: containersToRender.map(c => ({\n          id: c.id,\n          rteBoxValue: c.rteBoxValue\n        }))\n      });\n    } else {\n      console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\n      containersToRender = [];\n    }\n  } else {\n    // For non-AI content, use rtesContainer\n    containersToRender = rtesContainer;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\n        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        // For non-AI content, get data from rtesContainer\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const isCurrentlyEditing = editingRTEId === id;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          \".jodit-workplace\": {\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : selectedTemplate === \"Announcement\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\" ? \"calc(100vh - 400px) !important\" : null,\n            overflow: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"hidden\" : \"auto !important\"\n          },\n          \".jodit-container\": {\n            minWidth: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null\n          },\n          \".jodit-toolbar__box\": {\n            display: \"flex !important\",\n            justifyContent: \"center !important\",\n            height: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null\n          },\n          \"& .jodit-wysiwyg p\": {\n            margin: \"0 0 1em 0 !important\",\n            minHeight: \"1em !important\"\n          },\n          \"& .jodit-wysiwyg\": {\n            lineHeight: \"1.5 !important\"\n          }\n        },\n        className: \"qadpt-rte\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            maxWidth: \"100%\",\n            margin: \"0 auto\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => toggleToolbar(id),\n            sx: {\n              position: \"absolute\",\n              bottom: \"8px\",\n              right: \"8px\",\n              zIndex: 1000,\n              backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              width: \"32px\",\n              height: \"32px\",\n              \"&:hover\": {\n                backgroundColor: \"rgba(255, 255, 255, 1)\"\n              },\n              svg: {\n                width: \"16px\",\n                height: \"16px\",\n                path: {\n                  fill: toolbarVisible[id] === true ? \"var(--primarycolor)\" : \"#666\"\n                }\n              }\n            },\n            title: toolbarVisible[id] === true ? \"Hide Toolbar\" : \"Show Toolbar\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: settingsicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: createConfig(id),\n            onChange: newContent => handleUpdate(newContent, rteId, id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 33\n          }, this), (selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: \"absolute\",\n              top: \"8px\",\n              left: \"8px\",\n              zIndex: 1001,\n              display: \"flex\",\n              gap: \"4px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleCloneContainer(item.id),\n              disabled: isCloneDisabled,\n              title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n              sx: {\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                border: \"1px solid #ddd\",\n                borderRadius: \"4px\",\n                width: \"28px\",\n                height: \"28px\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                svg: {\n                  height: \"16px\",\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1,\n                  height: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleDeleteSection(item.id, rteId),\n              sx: {\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                border: \"1px solid #ddd\",\n                borderRadius: \"4px\",\n                width: \"28px\",\n                height: \"28px\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                svg: {\n                  height: \"16px\",\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  height: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 29\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n}, \"Rhks9kpGB5fxvi1g7k00qCNPlf8=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"Rhks9kpGB5fxvi1g7k00qCNPlf8=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "Box", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "settingsicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "toolbarVisible", "setToolbarVisible", "contentRef", "editor<PERSON><PERSON><PERSON>", "Map", "containerRefs", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "editor<PERSON><PERSON>", "setTimeout", "handleUpdate", "newContent", "containerId", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "console", "log", "substring", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "warn", "availableContainers", "map", "c", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "includes", "insertContent", "content", "editor", "selection", "insertHTML", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "toggleToolbar", "prev", "createConfig", "readonly", "direction", "language", "toolbarSticky", "toolbarAdaptive", "toolbar", "buttons", "name", "iconURL", "list", "autofocus", "cursorAfterAutofocus", "enter", "enterBlock", "defaultActionOnPaste", "useEnterForParagraph", "useSplitMode", "processPasteHTML", "askBeforePasteHTML", "askBeforePasteFromWord", "defaultLineHeight", "events", "onPaste", "afterInit", "e", "on", "key", "shift<PERSON>ey", "ctrl<PERSON>ey", "altKey", "s", "currentNode", "newParagraph", "createInside", "element", "innerHTML", "parentP", "parentNode", "insertBefore", "nextS<PERSON>ling", "append<PERSON><PERSON><PERSON>", "setCursorIn", "synchronizeValues", "fire", "controls", "font", "containersToRender", "_toolTipGuideMetaData7", "filter", "totalContainers", "length", "rteContainers", "rteData", "rteBoxValue", "children", "item", "rteText", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "isCurrentlyEditing", "currentEditorRef", "sx", "display", "alignItems", "position", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "minHeight", "maxHeight", "overflow", "justifyContent", "height", "margin", "lineHeight", "className", "style", "max<PERSON><PERSON><PERSON>", "onClick", "bottom", "right", "zIndex", "backgroundColor", "border", "borderRadius", "svg", "path", "fill", "title", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "config", "onChange", "top", "left", "gap", "size", "disabled", "opacity", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef } from \"react\";\r\nimport { Box, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, deleteicon, settingsicon } from \"../../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const [toolbarVisible, setToolbarVisible] = useState<{[key: string]: boolean}>({});\r\n        const contentRef = useRef<string>(\"\");\r\n\r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                editorRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Handle clicks outside the editor - now works with individual RTEs\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!editingRTEId) return; // No RTE is currently being edited\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                // Get the container ref for the currently editing RTE\r\n                const currentContainerRef = getContainerRef(editingRTEId);\r\n\r\n                // Check if the target is inside the currently editing RTE or related elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container\r\n                    !isInsidePopup && // Click outside the popup\r\n                    !isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n                    !isInsideWorkplacePopup && // Click outside the workplace popup\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isLinkPopup && // Click outside link input popup\r\n                    !isInsideToolbarButton && // Click outside the toolbar button\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [editingRTEId]);\r\n\r\n        useEffect(() => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    setTimeout(() => {\r\n                        //(editorRef.current as any).editor.focus();\r\n                    }, 50);\r\n                }\r\n            }\r\n        }, [editingRTEId]);\r\n\r\n\r\n\r\n        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            // Check if this is an AI-created guide\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            console.log(\"RTEsection handleUpdate:\", {\r\n                createWithAI,\r\n                selectedTemplate,\r\n                selectedTemplateTour,\r\n                isAIAnnouncement,\r\n                isAITour,\r\n                isTourBanner,\r\n                containerId,\r\n                newContent: newContent.substring(0, 50) + \"...\"\r\n            });\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    // For Tour+Announcement, use toolTipGuideMetaData\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        // Use the tooltip-specific handler for tour announcements\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    // For pure Announcements, use announcementGuideMetaData\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        // Use the announcement-specific handler\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    // Use the tooltip-specific handler for all tour step types\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\r\n                } else {\r\n                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\r\n                        currentStepIndex,\r\n                        containerId,\r\n                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))\r\n                    });\r\n                }\r\n            } else {\r\n                // For non-AI content or other cases, use the regular RTE container system\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n                console.log(\"Used updateRTEContainer for non-AI content\");\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n        };\r\n        const handleCloneContainer = (containerId: string) => {\r\n            // Check if cloning is disabled due to section limits\r\n            if (isCloneDisabled) {\r\n                return; // Don't clone if limit is reached\r\n            }\r\n\r\n            // Call the clone function from the store\r\n            cloneRTEContainer(containerId);\r\n\r\n            // Call the onClone callback if provided\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n        const handleDeleteSection = (containerId: string, rteId:string) => {\r\n            // Check if this is an AI-created announcement\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                // For AI announcements, we need to remove from announcementGuideMetaData\r\n                // This would require a new function in the store, for now just call the existing one\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                // For banners and non-AI content, use the regular clear function\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            // Call the handleDeleteRTESection callback to update section counts\r\n            handleDeleteRTESection(index);\r\n        };\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n\r\n        const insertContent = (content: string) => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        useEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n\r\n    // Function to toggle toolbar visibility for a specific RTE\r\n    const toggleToolbar = (rteId: string) => {\r\n        setToolbarVisible(prev => ({\r\n            ...prev,\r\n            [rteId]: !prev[rteId] // Toggle between true/false, defaults to false (hidden)\r\n        }));\r\n    };\r\n\r\n    // Function to create config for each RTE with toolbar visibility\r\n    const createConfig = (rteId: string) => ({\r\n        readonly: false, // all options from https://xdsoft.net/jodit/docs/,\r\n        direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n\r\n// Jodit uses 'direction' not just 'rtl'\r\n        language:  'en', // Optional: change language as well\r\n        toolbarSticky: false,\r\n        toolbarAdaptive: false,\r\n        toolbar: toolbarVisible[rteId] === true, // Hide toolbar by default, show when explicitly set to true\r\n        buttons: [\r\n\r\n        'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n        'font', 'fontsize', 'link',\r\n        {\r\n            name: 'more',\r\n            iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n            list: [\r\n                        'source',\r\n                        'image', 'video', 'table',\r\n                'align', 'undo', 'redo', '|',\r\n                'hr', 'eraser', 'copyformat',\r\n                'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n                'outdent', 'indent', 'paragraph',\r\n            ]\r\n        }\r\n    ],\r\n    autofocus: true,\r\n    cursorAfterAutofocus: 'end' as const,\r\n    enter: 'p' as const, // Use <p> tags for new lines instead of <div> or <br>\r\n    enterBlock: 'p' as const, // Ensure consistent block element creation\r\n    defaultActionOnPaste: 'insert_clear_html' as const, // Clean paste behavior\r\n    useEnterForParagraph: true, // Enable proper paragraph creation on Enter\r\n    useSplitMode: false, // Disable split mode to ensure proper line behavior\r\n    processPasteHTML: true, // Process HTML on paste\r\n    askBeforePasteHTML: false, // Don't ask before pasting HTML\r\n    askBeforePasteFromWord: false, // Don't ask before pasting from Word\r\n    defaultLineHeight: 1.5, // Set default line height\r\n    events: {\r\n        onPaste: handlePaste, // Attach custom onPaste handler\r\n        afterInit: (editor: any) => {\r\n            // Fix Enter key behavior for proper cursor movement\r\n            editor.e.on('keydown', (e: KeyboardEvent) => {\r\n                if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey) {\r\n                    e.preventDefault();\r\n\r\n                    const selection = editor.s;\r\n                    const currentNode = selection.current();\r\n\r\n                    // Create a new paragraph element\r\n                    const newParagraph = editor.createInside.element('p');\r\n                    newParagraph.innerHTML = '<br>';\r\n\r\n                    // If we're at the end of a paragraph or in an empty editor\r\n                    if (currentNode) {\r\n                        const parentP = currentNode.closest('p') || currentNode;\r\n                        if (parentP && parentP.parentNode) {\r\n                            // Insert the new paragraph after the current one\r\n                            parentP.parentNode.insertBefore(newParagraph, parentP.nextSibling);\r\n                        } else {\r\n                            // If no parent paragraph, append to the editor\r\n                            editor.editor.appendChild(newParagraph);\r\n                        }\r\n                    } else {\r\n                        // If no current node, append to the editor\r\n                        editor.editor.appendChild(newParagraph);\r\n                    }\r\n\r\n                    // Move cursor to the new paragraph\r\n                    selection.setCursorIn(newParagraph);\r\n\r\n                    // Trigger change event\r\n                    editor.synchronizeValues();\r\n                    editor.events.fire('change');\r\n\r\n                    return false; // Prevent default behavior\r\n                }\r\n            });\r\n        }\r\n    },\r\n    controls: {\r\n        font: {\r\n            list: {\r\n                \"Poppins, sans-serif\": \"Poppins\",\r\n                \"Roboto, sans-serif\": \"Roboto\",\r\n                \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                \"Open Sans, sans-serif\": \"Open Sans\",\r\n                \"Calibri, sans-serif\": \"Calibri\",\r\n                \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n            }\r\n        }\r\n            }\r\n    });\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            // For pure AI announcements (not in tours), use announcementGuideMetaData\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\r\n                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\r\n                    rteContainers: containersToRender.length,\r\n                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))\r\n                });\r\n            } else {\r\n                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            // For non-AI content, use rtesContainer\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\r\n                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        // For non-AI content, get data from rtesContainer\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n                    const isCurrentlyEditing = editingRTEId === id;\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                \".jodit-workplace\": {\r\n                                    minHeight: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    maxHeight: (\r\n  selectedTemplate === \"Banner\" ||\r\n  (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\")\r\n)\r\n  ? \"50px !important\"\r\n  : (\r\n      selectedTemplate === \"Announcement\" ||\r\n      (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\")\r\n    )\r\n    ? \"calc(100vh - 400px) !important\"\r\n    : null,\r\n                                    overflow: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ?\"hidden\" : \"auto !important\",\r\n                                },\r\n                                \".jodit-container\": {\r\n                                    minWidth:selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    minHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null\r\n                                },\r\n                                \".jodit-toolbar__box\": {\r\n                                    display: \"flex !important\",\r\n                                    justifyContent: \"center !important\",\r\n                                    height: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null,\r\n                                    maxHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null\r\n                                },\r\n                                \"& .jodit-wysiwyg p\": {\r\n                                    margin: \"0 0 1em 0 !important\",\r\n                                    minHeight: \"1em !important\",\r\n                                },\r\n                                \"& .jodit-wysiwyg\": {\r\n                                    lineHeight: \"1.5 !important\",\r\n                                }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {/* Always show Jodit editor with toolbar toggle */}\r\n                            <div style={{ width: \"100%\", maxWidth: \"100%\", margin: \"0 auto\", position: \"relative\" }}>\r\n                                {/* Toolbar toggle icon */}\r\n                                <IconButton\r\n                                    onClick={() => toggleToolbar(id)}\r\n                                    sx={{\r\n                                        position: \"absolute\",\r\n                                        bottom: \"8px\",\r\n                                        right: \"8px\",\r\n                                        zIndex: 1000,\r\n                                        backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                        border: \"1px solid #ddd\",\r\n                                        borderRadius: \"4px\",\r\n                                        width: \"32px\",\r\n                                        height: \"32px\",\r\n                                        \"&:hover\": {\r\n                                            backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                        },\r\n                                        svg: {\r\n                                            width: \"16px\",\r\n                                            height: \"16px\",\r\n                                            path: {\r\n                                                fill: toolbarVisible[id] === true ? \"var(--primarycolor)\" : \"#666\"\r\n                                            }\r\n                                        }\r\n                                    }}\r\n                                    title={toolbarVisible[id] === true ? \"Hide Toolbar\" : \"Show Toolbar\"}\r\n                                >\r\n                                    <span dangerouslySetInnerHTML={{ __html: settingsicon }} />\r\n                                </IconButton>\r\n\r\n                                <JoditEditor\r\n                                    ref={currentEditorRef}\r\n                                    value={rteText}\r\n                                    config={createConfig(id)}\r\n                                    onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                />\r\n\r\n                                {/* Clone and Delete buttons for Announcement/Tooltip/Hotspot */}\r\n                                {/* Clone and Delete buttons overlay for Announcement/Tooltip/Hotspot */}\r\n                                {((selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\") || (selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\")) && (\r\n                                    <div style={{\r\n                                        position: \"absolute\",\r\n                                        top: \"8px\",\r\n                                        left: \"8px\",\r\n                                        zIndex: 1001,\r\n                                        display: \"flex\",\r\n                                        gap: \"4px\"\r\n                                    }}>\r\n                                        <IconButton\r\n                                            size=\"small\"\r\n                                            onClick={() => handleCloneContainer(item.id)}\r\n                                            disabled={isCloneDisabled}\r\n                                            title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                            sx={{\r\n                                                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                                border: \"1px solid #ddd\",\r\n                                                borderRadius: \"4px\",\r\n                                                width: \"28px\",\r\n                                                height: \"28px\",\r\n                                                \"&:hover\": {\r\n                                                    backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                                },\r\n                                                svg: {\r\n                                                    height: \"16px\",\r\n                                                    path: {\r\n                                                        fill:\"var(--primarycolor)\"\r\n                                                    }\r\n                                                },\r\n                                            }}\r\n                                        >\r\n                                            <span\r\n                                                dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                                style={{\r\n                                                    opacity: isCloneDisabled ? 0.5 : 1,\r\n                                                    height: '16px'\r\n                                                }}\r\n                                            />\r\n                                        </IconButton>\r\n                                        <IconButton\r\n                                            size=\"small\"\r\n                                            onClick={() => handleDeleteSection(item.id, rteId)}\r\n                                            sx={{\r\n                                                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                                border: \"1px solid #ddd\",\r\n                                                borderRadius: \"4px\",\r\n                                                width: \"28px\",\r\n                                                height: \"28px\",\r\n                                                \"&:hover\": {\r\n                                                    backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                                },\r\n                                                svg: {\r\n                                                    height: \"16px\",\r\n                                                    path: {\r\n                                                        fill:\"var(--primarycolor)\"\r\n                                                    }\r\n                                                },\r\n                                            }}\r\n                                        >\r\n                                            <span dangerouslySetInnerHTML={{ __html: deleteicon }}\r\n                                                style={{\r\n                                                    height: '16px'\r\n                                                }}\r\n                                            />\r\n                                        </IconButton>\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACtE,SAASC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAC/C,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,QAAQ,6BAA6B;AAChF,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGd,UAAU,CAAAe,EAAA,GAAAD,EAAA,CACpD,CAAC;EAAEE,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAAT,EAAA;EACvG,MAAM;IAAEU,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM;IACFkB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAC,GAAGnC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAA2B,CAAC,CAAC,CAAC;EAClF,MAAM+C,UAAU,GAAG7C,MAAM,CAAS,EAAE,CAAC;;EAErC;EACA,MAAM8C,UAAU,GAAG9C,MAAM,CAAoC,IAAI+C,GAAG,CAAC,CAAC,CAAC;EACvE,MAAMC,aAAa,GAAGhD,MAAM,CAA+C,IAAI+C,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAME,YAAY,GAAIC,KAAa,IAAK;IACpC,IAAI,CAACJ,UAAU,CAACK,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAChCJ,UAAU,CAACK,OAAO,CAACE,GAAG,CAACH,KAAK,eAAErD,KAAK,CAACyD,SAAS,CAAC,CAAC,CAAC;IACpD;IACA,OAAOR,UAAU,CAACK,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMM,eAAe,GAAIN,KAAa,IAAK;IACvC,IAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACnCF,aAAa,CAACG,OAAO,CAACE,GAAG,CAACH,KAAK,eAAErD,KAAK,CAACyD,SAAS,CAAC,CAAC,CAAC;IACvD;IACA,OAAON,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EAC3C,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACZ,MAAM0D,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC9C,IAAI,CAACrB,YAAY,EAAE,OAAO,CAAC;;MAE3B,MAAMsB,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,cAAAV,qBAAA,uBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMO,kBAAkB,IAAAX,sBAAA,GAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,cAAAT,sBAAA,uBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMQ,sBAAsB,GAAGD,kBAAkB,MAAAV,sBAAA,GAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,cAAAR,sBAAA,uBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMS,iBAAiB,GAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAd,sBAAA,GAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,cAAAP,sBAAA,uBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMa,qBAAqB,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMa,cAAc,GAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,MAAMc,mBAAmB,GAAGvB,eAAe,CAACf,YAAY,CAAC;;MAEzD;MACA,IACIsC,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE5B,OAAO,IAC5B,CAAC4B,mBAAmB,CAAC5B,OAAO,CAACmB,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAAI;MAC/D,CAACG,aAAa;MAAI;MAClB,CAACI,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACG,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAI;MAC1B,CAACC,cAAc,IACf,CAACf,yBAAyB,IAC1B,CAACG,oBAAoB,EACvB;QACExB,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3B;IACJ,CAAC;IAED0B,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAAChB,YAAY,CAAC,CAAC;EAElB1C,SAAS,CAAC,MAAM;IACZ,IAAI0C,YAAY,EAAE;MACd,MAAMyC,SAAS,GAAGjC,YAAY,CAACR,YAAY,CAAC;MAC5C,IAAIyC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpBgC,UAAU,CAAC,MAAM;UACb;QAAA,CACH,EAAE,EAAE,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAAC1C,YAAY,CAAC,CAAC;EAIlB,MAAM2C,YAAY,GAAGA,CAACC,UAAkB,EAAEnC,KAAa,EAAEoC,WAAmB,KAAK;IAC7EzC,UAAU,CAACM,OAAO,GAAGkC,UAAU;;IAE/B;IACA,MAAME,gBAAgB,GAAGjD,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IACzH,MAAMuD,QAAQ,GAAGlD,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAMyD,kBAAkB,GAAGD,QAAQ,IAAIvD,oBAAoB,KAAK,cAAc;IAC9E,MAAMyD,YAAY,GAAGF,QAAQ,IAAIvD,oBAAoB,KAAK,QAAQ;IAClE,MAAM0D,aAAa,GAAGH,QAAQ,KAAKvD,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5G2D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACpCvD,YAAY;MACZN,gBAAgB;MAChBC,oBAAoB;MACpBsD,gBAAgB;MAChBC,QAAQ;MACRE,YAAY;MACZJ,WAAW;MACXD,UAAU,EAAEA,UAAU,CAACS,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;IAC9C,CAAC,CAAC;IAEF,IAAIP,gBAAgB,EAAE;MAClB,MAAMQ,gBAAgB,GAAGxD,WAAW,GAAG,CAAC;MAExC,IAAIkD,kBAAkB,EAAE;QAAA,IAAAO,qBAAA,EAAAC,sBAAA;QACpB;QACA,MAAMC,gBAAgB,IAAAF,qBAAA,GAAG7D,oBAAoB,CAAC4D,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UAClB;UACA7D,qBAAqB,CAACiD,WAAW,EAAED,UAAU,CAAC;QAClD;MACJ,CAAC,MAAM;QAAA,IAAAkB,qBAAA,EAAAC,sBAAA;QACH;QACA,MAAMC,qBAAqB,IAAAF,qBAAA,GAAGrE,yBAAyB,CAAC6D,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACvB;UACArE,0BAA0B,CAACkD,WAAW,EAAED,UAAU,CAAC;QACvD;MACJ;IACJ,CAAC,MAAM,IAAIG,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAe,sBAAA,EAAAC,sBAAA;MACpD;MACA,MAAMZ,gBAAgB,GAAGxD,WAAW,GAAG,CAAC;MACxC,MAAM2D,gBAAgB,IAAAQ,sBAAA,GAAGvE,oBAAoB,CAAC4D,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QAClB;QACA7D,qBAAqB,CAACiD,WAAW,EAAED,UAAU,CAAC;QAC9CO,OAAO,CAACC,GAAG,CAAC,kCAAkC5D,oBAAoB,kBAAkB,CAAC;MACzF,CAAC,MAAM;QAAA,IAAA2E,sBAAA,EAAAC,sBAAA;QACHjB,OAAO,CAACkB,IAAI,CAAC,kCAAkC7E,oBAAoB,OAAO,EAAE;UACxE8D,gBAAgB;UAChBT,WAAW;UACXyB,mBAAmB,GAAAH,sBAAA,GAAEzE,oBAAoB,CAAC4D,gBAAgB,CAAC,cAAAa,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCT,UAAU,cAAAU,sBAAA,uBAAlDA,sBAAA,CAAoDG,GAAG,CAACC,CAAC,KAAK;YAAEvC,EAAE,EAAEuC,CAAC,CAACvC,EAAE;YAAE4B,IAAI,EAAEW,CAAC,CAACX;UAAK,CAAC,CAAC;QAClH,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH;MACA1E,kBAAkB,CAAC0D,WAAW,EAAEpC,KAAK,EAAEmC,UAAU,CAAC;MAClDO,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC7D;IAEAhE,mBAAmB,CAAC,IAAI,CAAC;EAC7B,CAAC;EACD,MAAMqF,oBAAoB,GAAI5B,WAAmB,IAAK;IAClD;IACA,IAAI/D,eAAe,EAAE;MACjB,OAAO,CAAC;IACZ;;IAEA;IACAO,iBAAiB,CAACwD,WAAW,CAAC;;IAE9B;IACA,IAAIhE,OAAO,EAAE;MACTA,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,MAAM6F,mBAAmB,GAAGA,CAAC7B,WAAmB,EAAEpC,KAAY,KAAK;IAC/D;IACA,MAAMqC,gBAAgB,GAAGjD,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAEzH,IAAIsD,gBAAgB,EAAE;MAClB;MACA;MACAxD,eAAe,CAACuD,WAAW,EAAEpC,KAAK,CAAC;IACvC,CAAC,MAAM;MACH;MACAnB,eAAe,CAACuD,WAAW,EAAEpC,KAAK,CAAC;IACvC;;IAEA;IACA/B,sBAAsB,CAACC,KAAK,CAAC;EACjC,CAAC;EACD,MAAMgG,WAAW,GAAI1D,KAA2C,IAAK;IACjEA,KAAK,CAAC2D,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAG5D,KAAK,CAAC4D,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACZ,MAAMC,YAAY,GAAGD,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAID,YAAY,EAAE;QACdE,aAAa,CAACH,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHG,aAAa,CAACH,UAAU,CAAC;MAC7B;IACJ,CAAC,MAAM;MACHG,aAAa,CAACL,UAAU,CAAC;IAC7B;EACJ,CAAC;EAGD,MAAMK,aAAa,GAAIC,OAAe,IAAK;IACvC,IAAIpF,YAAY,EAAE;MACd,MAAMyC,SAAS,GAAGjC,YAAY,CAACR,YAAY,CAAC;MAC5C,IAAIyC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpB,MAAM2E,MAAM,GAAI5C,SAAS,CAAC/B,OAAO,CAAS2E,MAAM;QAChDA,MAAM,CAACC,SAAS,CAACC,UAAU,CAACH,OAAO,CAAC;MACxC;IACJ;EACJ,CAAC;EACD,MAAM,CAACI,cAAc,EAAEC,iBAAiB,CAAC,GAAGpI,QAAQ,CAAU,KAAK,CAAC;EACpEC,SAAS,CAAC,MAAM;IACpB,MAAMoI,GAAG,GAAG/D,QAAQ,CAACgE,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAIrF,KAAa,IAAK;IACrCN,iBAAiB,CAAC4F,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACtF,KAAK,GAAG,CAACsF,IAAI,CAACtF,KAAK,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMuF,YAAY,GAAIvF,KAAa,KAAM;IACrCwF,QAAQ,EAAE,KAAK;IAAE;IACjBC,SAAS,EAAEV,cAAc,GAAG,KAAK,GAAY,KAAc;IAEnE;IACQW,QAAQ,EAAG,IAAI;IAAE;IACjBC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAEpG,cAAc,CAACO,KAAK,CAAC,KAAK,IAAI;IAAE;IACzC8F,OAAO,EAAE,CAET,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACIC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACM,QAAQ,EACR,OAAO,EAAE,OAAO,EAAE,OAAO,EACjC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAC9D,SAAS,EAAE,QAAQ,EAAE,WAAW;IAExC,CAAC,CACJ;IACDC,SAAS,EAAE,IAAI;IACfC,oBAAoB,EAAE,KAAc;IACpCC,KAAK,EAAE,GAAY;IAAE;IACrBC,UAAU,EAAE,GAAY;IAAE;IAC1BC,oBAAoB,EAAE,mBAA4B;IAAE;IACpDC,oBAAoB,EAAE,IAAI;IAAE;IAC5BC,YAAY,EAAE,KAAK;IAAE;IACrBC,gBAAgB,EAAE,IAAI;IAAE;IACxBC,kBAAkB,EAAE,KAAK;IAAE;IAC3BC,sBAAsB,EAAE,KAAK;IAAE;IAC/BC,iBAAiB,EAAE,GAAG;IAAE;IACxBC,MAAM,EAAE;MACJC,OAAO,EAAE5C,WAAW;MAAE;MACtB6C,SAAS,EAAGnC,MAAW,IAAK;QACxB;QACAA,MAAM,CAACoC,CAAC,CAACC,EAAE,CAAC,SAAS,EAAGD,CAAgB,IAAK;UACzC,IAAIA,CAAC,CAACE,GAAG,KAAK,OAAO,IAAI,CAACF,CAAC,CAACG,QAAQ,IAAI,CAACH,CAAC,CAACI,OAAO,IAAI,CAACJ,CAAC,CAACK,MAAM,EAAE;YAC7DL,CAAC,CAAC7C,cAAc,CAAC,CAAC;YAElB,MAAMU,SAAS,GAAGD,MAAM,CAAC0C,CAAC;YAC1B,MAAMC,WAAW,GAAG1C,SAAS,CAAC5E,OAAO,CAAC,CAAC;;YAEvC;YACA,MAAMuH,YAAY,GAAG5C,MAAM,CAAC6C,YAAY,CAACC,OAAO,CAAC,GAAG,CAAC;YACrDF,YAAY,CAACG,SAAS,GAAG,MAAM;;YAE/B;YACA,IAAIJ,WAAW,EAAE;cACb,MAAMK,OAAO,GAAGL,WAAW,CAACxG,OAAO,CAAC,GAAG,CAAC,IAAIwG,WAAW;cACvD,IAAIK,OAAO,IAAIA,OAAO,CAACC,UAAU,EAAE;gBAC/B;gBACAD,OAAO,CAACC,UAAU,CAACC,YAAY,CAACN,YAAY,EAAEI,OAAO,CAACG,WAAW,CAAC;cACtE,CAAC,MAAM;gBACH;gBACAnD,MAAM,CAACA,MAAM,CAACoD,WAAW,CAACR,YAAY,CAAC;cAC3C;YACJ,CAAC,MAAM;cACH;cACA5C,MAAM,CAACA,MAAM,CAACoD,WAAW,CAACR,YAAY,CAAC;YAC3C;;YAEA;YACA3C,SAAS,CAACoD,WAAW,CAACT,YAAY,CAAC;;YAEnC;YACA5C,MAAM,CAACsD,iBAAiB,CAAC,CAAC;YAC1BtD,MAAM,CAACiC,MAAM,CAACsB,IAAI,CAAC,QAAQ,CAAC;YAE5B,OAAO,KAAK,CAAC,CAAC;UAClB;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;IACDC,QAAQ,EAAE;MACNC,IAAI,EAAE;QACFpC,IAAI,EAAE;UACF,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAClC;MACJ;IACI;EACR,CAAC,CAAC;;EAEE;EACA,MAAM5D,gBAAgB,GAAGjD,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAMuD,QAAQ,GAAGlD,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAMyD,kBAAkB,GAAGD,QAAQ,IAAIvD,oBAAoB,KAAK,cAAc;EAC9E,MAAMyD,YAAY,GAAGF,QAAQ,IAAIvD,oBAAoB,KAAK,QAAQ;EAClE,MAAM0D,aAAa,GAAGH,QAAQ,KAAKvD,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAM8D,gBAAgB,GAAGxD,WAAW,GAAG,CAAC;EAExC,IAAIiJ,kBAAyB,GAAG,EAAE;EAElC,IAAIjG,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IACzC;IACA+F,kBAAkB,GAAGhJ,8BAA8B,CAACuD,gBAAgB,EAAE,KAAK,CAAC;EAChF,CAAC,MAAM,IAAIP,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAAgG,sBAAA;IAC1E;IACA,KAAAA,sBAAA,GAAItJ,oBAAoB,CAAC4D,gBAAgB,CAAC,cAAA0F,sBAAA,eAAtCA,sBAAA,CAAwCtF,UAAU,EAAE;MACpDqF,kBAAkB,GAAGrJ,oBAAoB,CAAC4D,gBAAgB,CAAC,CAACI,UAAU,CAACuF,MAAM,CAACzE,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAK,KAAK,CAAC;MACpGV,OAAO,CAACC,GAAG,CAAC,yDAAyD5D,oBAAoB,SAAS8D,gBAAgB,GAAG,EAAE;QACnH4F,eAAe,EAAExJ,oBAAoB,CAAC4D,gBAAgB,CAAC,CAACI,UAAU,CAACyF,MAAM;QACzEC,aAAa,EAAEL,kBAAkB,CAACI,MAAM;QACxCE,OAAO,EAAEN,kBAAkB,CAACxE,GAAG,CAACC,CAAC,KAAK;UAAEvC,EAAE,EAAEuC,CAAC,CAACvC,EAAE;UAAEqH,WAAW,EAAE9E,CAAC,CAAC8E;QAAY,CAAC,CAAC;MACnF,CAAC,CAAC;IACN,CAAC,MAAM;MACHnG,OAAO,CAACkB,IAAI,CAAC,iDAAiD7E,oBAAoB,SAAS8D,gBAAgB,EAAE,CAAC;MAC9GyF,kBAAkB,GAAG,EAAE;IAC3B;EACJ,CAAC,MAAM;IACH;IACAA,kBAAkB,GAAG7J,aAAa;EACtC;EAEA,oBACIhB,OAAA,CAAAE,SAAA;IAAAmL,QAAA,EACKR,kBAAkB,CAACxE,GAAG,CAAEiF,IAAS,IAAK;MACnC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIhJ,KAAK,GAAG,EAAE;MACd,IAAIwB,EAAE,GAAG,EAAE;MAEX,IAAKa,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH;QACA;QACAyG,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChC7I,KAAK,GAAG+I,IAAI,CAACvH,EAAE;QACfA,EAAE,GAAGuH,IAAI,CAACvH,EAAE;MAChB,CAAC,MAAM;QAAA,IAAAyH,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACH;QACAJ,OAAO,GAAG,EAAAC,UAAA,GAAAF,IAAI,CAACM,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpCtJ,KAAK,IAAAmJ,WAAA,GAAGJ,IAAI,CAACM,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgB5H,EAAE;QAC1BA,EAAE,GAAGuH,IAAI,CAACvH,EAAE;MAChB;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAEpB,MAAM+H,kBAAkB,GAAGhK,YAAY,KAAKiC,EAAE;MAC9C,MAAMK,mBAAmB,GAAGvB,eAAe,CAACkB,EAAE,CAAC;MAC/C,MAAMgI,gBAAgB,GAAGzJ,YAAY,CAACyB,EAAE,CAAC;MAEzC,oBACI/D,OAAA,CAACT,GAAG;QAEAsB,GAAG,EAAEuD,mBAAoB;QACzB4H,EAAE,EAAE;UACAC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB,0BAA0B,EAAE;YACxBF,OAAO,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACfG,UAAU,EAAE;UAChB,CAAC;UACD,oBAAoB,EAAE;YAClBA,UAAU,EAAE;UAChB,CAAC;UACD,8BAA8B,EAAE;YAC5BC,QAAQ,EAAE;UACd,CAAC;UACD,wBAAwB,EAAE;YACtBC,KAAK,EAAEjL,gBAAgB,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;YACtDkL,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,kBAAkB,EAAE;YAChBC,SAAS,EAAEpL,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACtIoL,SAAS,EAC3CrL,gBAAgB,KAAK,QAAQ,IAC5BA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAEhE,iBAAiB,GAEfD,gBAAgB,KAAK,cAAc,IAClCA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,cAAe,GAExE,gCAAgC,GAChC,IAAI;YAC0BqL,QAAQ,EAAEtL,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAE,QAAQ,GAAG;UAC5H,CAAC;UACD,kBAAkB,EAAE;YAChB+K,QAAQ,EAAChL,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACpImL,SAAS,EAAEpL,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD,qBAAqB,EAAE;YACnB2K,OAAO,EAAE,iBAAiB;YAC1BW,cAAc,EAAE,mBAAmB;YACnCC,MAAM,EAAExL,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACnIoL,SAAS,EAAErL,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD,oBAAoB,EAAE;YAClBwL,MAAM,EAAE,sBAAsB;YAC9BL,SAAS,EAAE;UACf,CAAC;UACD,kBAAkB,EAAE;YAChBM,UAAU,EAAE;UAChB;QACJ,CAAE;QACFC,SAAS,EAAC,WAAW;QAAA3B,QAAA,eAGrBrL,OAAA;UAAKiN,KAAK,EAAE;YAAEX,KAAK,EAAE,MAAM;YAAEY,QAAQ,EAAE,MAAM;YAAEJ,MAAM,EAAE,QAAQ;YAAEX,QAAQ,EAAE;UAAW,CAAE;UAAAd,QAAA,gBAEpFrL,OAAA,CAACR,UAAU;YACP2N,OAAO,EAAEA,CAAA,KAAMvF,aAAa,CAAC7D,EAAE,CAAE;YACjCiI,EAAE,EAAE;cACAG,QAAQ,EAAE,UAAU;cACpBiB,MAAM,EAAE,KAAK;cACbC,KAAK,EAAE,KAAK;cACZC,MAAM,EAAE,IAAI;cACZC,eAAe,EAAE,0BAA0B;cAC3CC,MAAM,EAAE,gBAAgB;cACxBC,YAAY,EAAE,KAAK;cACnBnB,KAAK,EAAE,MAAM;cACbO,MAAM,EAAE,MAAM;cACd,SAAS,EAAE;gBACPU,eAAe,EAAE;cACrB,CAAC;cACDG,GAAG,EAAE;gBACDpB,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACdc,IAAI,EAAE;kBACFC,IAAI,EAAE5L,cAAc,CAAC+B,EAAE,CAAC,KAAK,IAAI,GAAG,qBAAqB,GAAG;gBAChE;cACJ;YACJ,CAAE;YACF8J,KAAK,EAAE7L,cAAc,CAAC+B,EAAE,CAAC,KAAK,IAAI,GAAG,cAAc,GAAG,cAAe;YAAAsH,QAAA,eAErErL,OAAA;cAAM8N,uBAAuB,EAAE;gBAAEC,MAAM,EAAElO;cAAa;YAAE;cAAAmO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEbnO,OAAA,CAACP,WAAW;YACRoB,GAAG,EAAEkL,gBAAiB;YACtBqC,KAAK,EAAE7C,OAAQ;YACf8C,MAAM,EAAEvG,YAAY,CAAC/D,EAAE,CAAE;YACzBuK,QAAQ,EAAG5J,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAEnC,KAAK,EAAEwB,EAAE;UAAE;YAAAiK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,EAID,CAAE9M,gBAAgB,KAAK,cAAc,IAAIA,gBAAgB,KAAK,SAAS,IAAIA,gBAAgB,KAAK,SAAS,IAAMC,oBAAoB,KAAK,cAAc,IAAIA,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAU,kBAChOtB,OAAA;YAAKiN,KAAK,EAAE;cACRd,QAAQ,EAAE,UAAU;cACpBoC,GAAG,EAAE,KAAK;cACVC,IAAI,EAAE,KAAK;cACXlB,MAAM,EAAE,IAAI;cACZrB,OAAO,EAAE,MAAM;cACfwC,GAAG,EAAE;YACT,CAAE;YAAApD,QAAA,gBACErL,OAAA,CAACR,UAAU;cACPkP,IAAI,EAAC,OAAO;cACZvB,OAAO,EAAEA,CAAA,KAAM5G,oBAAoB,CAAC+E,IAAI,CAACvH,EAAE,CAAE;cAC7C4K,QAAQ,EAAE/N,eAAgB;cAC1BiN,KAAK,EAAEjN,eAAe,GAAGG,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;cACjHiL,EAAE,EAAE;gBACAuB,eAAe,EAAE,0BAA0B;gBAC3CC,MAAM,EAAE,gBAAgB;gBACxBC,YAAY,EAAE,KAAK;gBACnBnB,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE;kBACPU,eAAe,EAAE;gBACrB,CAAC;gBACDG,GAAG,EAAE;kBACDb,MAAM,EAAE,MAAM;kBACdc,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACJ,CAAE;cAAAvC,QAAA,eAEFrL,OAAA;gBACI8N,uBAAuB,EAAE;kBAAEC,MAAM,EAAEpO;gBAAS,CAAE;gBAC9CsN,KAAK,EAAE;kBACH2B,OAAO,EAAEhO,eAAe,GAAG,GAAG,GAAG,CAAC;kBAClCiM,MAAM,EAAE;gBACZ;cAAE;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACbnO,OAAA,CAACR,UAAU;cACPkP,IAAI,EAAC,OAAO;cACZvB,OAAO,EAAEA,CAAA,KAAM3G,mBAAmB,CAAC8E,IAAI,CAACvH,EAAE,EAAExB,KAAK,CAAE;cACnDyJ,EAAE,EAAE;gBACAuB,eAAe,EAAE,0BAA0B;gBAC3CC,MAAM,EAAE,gBAAgB;gBACxBC,YAAY,EAAE,KAAK;gBACnBnB,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE;kBACPU,eAAe,EAAE;gBACrB,CAAC;gBACDG,GAAG,EAAE;kBACDb,MAAM,EAAE,MAAM;kBACdc,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACJ,CAAE;cAAAvC,QAAA,eAEFrL,OAAA;gBAAM8N,uBAAuB,EAAE;kBAAEC,MAAM,EAAEnO;gBAAW,CAAE;gBAClDqN,KAAK,EAAE;kBACHJ,MAAM,EAAE;gBACZ;cAAE;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC,GArKDpK,EAAE;QAAAiK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsKN,CAAC;IAEd,CAAC;EAAC,gBACJ,CAAC;AAEX,CAAC;EAAA,QApjB4BrO,cAAc,EAgBnCJ,cAAc;AAAA,EAqiB1B,CAAC;EAAA,QArjBgCI,cAAc,EAgBnCJ,cAAc;AAAA,EAqiBzB;AAACmP,GAAA,GAvjBI1O,UAAqC;AAyjB3C,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAwO,GAAA;AAAAC,YAAA,CAAAzO,EAAA;AAAAyO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}